from django.db import models
from django.utils.translation import gettext_lazy as _

from suzuka.conf.models import Feature


class AdServingFeature(Feature):
    title = "Ad Serving"
    default_enabled = True
    urlpatterns = ("dartiframe", "addineyev2")
    FUSE_LIBRARY_VERSIONS = [
        ("2001", "2001"),
        ("2003", "2003"),
    ]

    # DFP lazy loading settings
    fetch_margin_percent = models.PositiveSmallIntegerField(
        "Lazy loading fetch margin percent",
        help_text="Leave blank to use defaults (200%)",
        blank=True,
        null=True,
    )
    mobile_scaling = models.PositiveSmallIntegerField(
        "Lazy loading mobile scaling",
        help_text="Leave blank to use defaults (2)",
        blank=True,
        null=True,
    )
    render_margin_percent = models.PositiveSmallIntegerField(
        "Lazy loading render margin percent",
        help_text="Leave blank to use defaults (100%)",
        blank=True,
        null=True,
    )
    use_mantis = models.BooleanField(
        "Enable Mantis",
        help_text="Enable to use Mantis for ad brand safety",
        default=False,
    )
    use_bottom_anchor_ad = models.BooleanField(
        "Enable Bottom Anchor Ad",
        help_text="Enable to use Bottom Anchor Ad",
        default=False,
    )
    bottom_anchor_ad_position = models.PositiveSmallIntegerField(
        "Bottom Anchor Ad Position",
        help_text="Position value that will be used to target the Bottom Anchor Ad",
        default=1,
    )
    auto_refresh_interval = models.PositiveSmallIntegerField(
        "Auto refresh interval (seconds)",
        default=0,
        help_text="0 to disable",
    )
    enable_lazy_load_ab_test = models.BooleanField(
        "Enable lazy loading A/B test",
        default=False,
    )
    aps_publisher_id = models.CharField(
        "Amazon Publisher Services publisher ID",
        max_length=36,  # UUID
        default="",
        blank=True,
    )
    use_publift = models.BooleanField("Use Publift", default=False)
    fuse_library_version = models.CharField(
        _("Fuse Library Version"),
        max_length=4,
        choices=FUSE_LIBRARY_VERSIONS,
        default="2001",
        help_text=_(
            "Select the version of the Fuse library to use. This affects the URL of the Fuse script in the header."
        ),
    )


class OovvuuFeature(Feature):
    """Oovvuu video recommendation ads."""

    title = "Oovvuu"
    default_enabled = False
