"""A Django management command to configure a site to be Autumn themed."""

import os
import re
from urllib.parse import urlparse

from bs4 import BeautifulSoup
from django.contrib.sites.models import Site
from django.core.management.base import BaseCommand, CommandError
from django.db import models, transaction
from django.db.models import Count, OuterRef, Q, Subquery
from shared_orgs_client import api as shared_orgs_api

from suzuka.elements_demo.models import (
    Classified,
    ClusteredStoryList,
    CodeSnippet,
    DPEList,
    Heading,
    MailingList,
    MenuList,
    Navigation,
    StoryList as StoryListElement,
    TitledStoryList,
)
from suzuka.pages.models import Page, SitePage, ZoneItem
from suzuka.pages.utils import SCORES_AND_DRAWS_TEMPLATE
from suzuka.stories.models import StoryList, StoryListSite

THEME = "autumn"
CLEAN_SITE_NAME_REGEX = re.compile(r"^zBeta\s|\sBeta.*", re.IGNORECASE)
NAVIGATION_ZONE = "site_wide_navigation"
NEWS_WELL_ZONE = "newswell"
NEWS_WELL_SIDE_ZONE = "newswell-side"
MAIN_ZONE = "main"
MAIN_SIDE_ZONE = "main-side"
FOOTER_ZONE = "footer_global"
RIGHT_ZONE = "right"
TOP_RIGHT = "top right"
FOOTER_NAV_TEMPLATE = "footer_nav.html"
STORY_LIST_MAIN_TEMPLATE = "main.html"
NEWSWELL_STORY_LIST_TEMPLATE = "newswell.html"
LIST_VIEW_LOAD_MORE_TEMPLATE = "listview-load-more.html"
BUSINESS_FEATURE_TEMPLATE = "businessfeature.html"
NEWSWELL_SIDE_STORY_LIST_TEMPLATE = "default.html"
DEFAULT_MAILING_LIST_TEMPLATE = "default.html"
NEWSWELL_SIDE_MAILING_LIST_TEMPLATE = DEFAULT_MAILING_LIST_TEMPLATE
MENU_LIST_TEMPLATE = "strap_heading.html"
LIST_VIEW_LOAD_MORE_TEMPLATE = "listview-load-more.html"
PRIMARY_INDEX_PAGE_TEMPLATE = "index_page.html"
SECONDARY_INDEX_PAGE_TEMPLATE = "index_page_2nd_level.html"
REV_STRAP_ZONE_ITEM_TEMPLATE = "rev_strap_heading.html"
WEATHER_ZONE_TEMPLATE = "weatherzone.html"
LEGO_REALESTATE_VIEW_TEMPLATE = "realestate-view.html"
REALESTATE_VIEW_TEMPLATE = "realestateview.html"
CUSTOM_HEADING_TEMPLATE = "heading1.html"
DEFAULT_ZONE_ITEM_TEMPLATE = "default.html"
EXPLORE_STRAP_TEMPLATE = "explore.html"

# Ad settings
FETCH_MARGIN_PERCENT = 100
MOBILE_SCALING = 1
RENDER_MARGIN_PERCENT = 25

AGS_ZONES_DELETE_ALL = [
    "footer_global",
    "main",
    "site_navigation",
    "storypage_bottom",
    "storypage_footer",
]

MAIN_STORY_LIST_ZONE_TEMPLATE_LIMITS = {
    "bf-strap-1s-1s-1s.html": 10,
    "strap-1s-1s-1s.html": 3,
    "strap-1-1-1.html": 3,
    "strap-2o-2o-4.html": 8,
    "strap-2-1o-2.html": 5,
    "realestateview.html": 5,
}

PRIMARY_NAV_PAGES = [
    "News",
    "Sport",
    "What's On",
    "Comment",
    "Jobs",
    "Classifieds",
]

DAILY_DOMAINS = [
    "newcastleherald.com.au",
    "canberratimes.com.au",
    "illawarramercury.com.au",
    "northerndailyleader.com.au",
    "theadvocate.com.au",
    "examiner.com.au",
    "newcastleherald.com.au",
    "bordermail.com.au",
    "dailyadvertiser.com.au",
    "bendigoadvertiser.com.au",
    "thecourier.com.au",
    "standard.net.au",
    "centralwesterndaily.com.au",
    "dailyliberal.com.au",
    "westernadvocate.com.au",
]

DOMAIN_CONFIG = {
    "theland.com.au": {
        "dpe_id": "TL",
    },
    "queenslandcountrylife.com.au": {
        "dpe_id": "QC",
    },
    "stockandland.com.au": {
        "dpe_id": "STL",
    },
    "stockjournal.com.au": {
        "dpe_id": "SJ",
    },
    "northqueenslandregister.com.au": {
        "dpe_id": "NQ",
    },
    "goodfruitandvegetables.com.au": {
        "dpe_id": "GFV",
    },
}

# From: https://docs.google.com/spreadsheets/d/1EOml5Ssvzrtt73DV98kd-zsllKCI-2rgJv4z1V8B33U/edit?pli=1#gid=657586119
DOMAIN_TO_REV = {
    "advertiserlaketimes.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-shellharbour-2529/",
    "araratadvertiser.com.au": "https://www.realestateview.com.au/for-sale/in-vic-ararat-3377/",
    "areanews.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-griffith-2680/",
    "armidaleexpress.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-armidale-2350/",
    "avonadvocate.com.au": "https://www.realestateview.com.au/for-sale/in-wa-northam-6401/",
    "barossaherald.com.au": "https://www.realestateview.com.au/for-sale/in-sa-altona-5351/",
    "batemansbaypost.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-batemans-bay-2536/",
    "beaudeserttimes.com.au": "https://www.realestateview.com.au/for-sale/in-qld-southport-4215/",
    "begadistrictnews.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-bega-2550/",
    "bellingencourier.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-bellingen-2454/",
    "bendigoadvertiser.com.au": "https://www.realestateview.com.au/for-sale/in-vic-bendigo-3550",
    "blayneychronicle.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-blayney-2799/",
    "bluemountainsgazette.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-wentworth-falls-2782/",
    "bombalatimes.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-bombala-2632/",
    "boorowanewsonline.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-boorowa-2586/",
    "borderchronicle.com.au": "https://www.realestateview.com.au/for-sale/in-sa-bordertown-5268/",
    "bordermail.com.au": "https://www.realestateview.com.au/for-sale/in-vic-wodonga-3690/",
    "braidwoodtimes.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-braidwood-2622/",
    "bunburymail.com.au": "https://www.realestateview.com.au/for-sale/in-wa-bunbury-6230/",
    "busseltonmail.com.au": "https://www.realestateview.com.au/for-sale/in-wa-busselton-6280/",
    "camdenadvertiser.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-camden-2570/",
    "camdencourier.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-laurieton-2443/",
    "canowindranews.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-canowindra-2804/",
    "centralwesterndaily.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-orange-2800/",
    "cessnockadvertiser.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-cessnock-2325/",
    "coastalleader.com.au": "https://www.realestateview.com.au/for-sale/in-sa-kingston-park-5049/",
    "colliemail.com.au": "https://www.realestateview.com.au/for-sale/in-wa-collie-6225/",
    "colypointobserver.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-coal-point-2283/",
    "cootamundraherald.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-cootamundra-2590/",
    "cowraguardian.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-cowra-2794/",
    "crookwellgazette.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-crookwell-2583/",
    "dailyadvertiser.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-wagga-wagga-2650/",
    "dailyliberal.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-dubbo-2830/",
    "donnybrookmail.com.au": "https://www.realestateview.com.au/for-sale/in-wa-donnybrook-6239/",
    "dungogchronicle.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-dungog-2420/",
    "easternriverinachronicle.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-henty-2658/",
    "edenmagnet.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-eden-2551/",
    "esperanceexpress.com.au": "https://www.realestateview.com.au/for-sale/in-wa-esperance-6450/",
    "examiner.com.au": "https://www.realestateview.com.au/for-sale/in-tas-launceston-7250/",
    "eyretribune.com.au": "https://www.realestateview.com.au/for-sale/in-sa-cleve-5640/",
    "fairfieldchampion.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-fairfield-2165/",
    "forbesadvocate.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-forbes-2871/",
    "gleninnesexaminer.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-glen-innes-2370/",
    "gloucesteradvocate.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-gloucester-2422/",
    "goondiwindiargus.com.au": "https://www.realestateview.com.au/for-sale/in-qld-goondiwindi-4390/",
    "goulburnpost.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-goulburn-2580/",
    "greatlakesadvocate.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-forster-2428/",
    "grenfellrecord.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-grenfell-2810/",
    "guyraargus.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-guyra-2365/",
    "hardenexpress.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-harden-2587/",
    "hawkesburygazette.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-richmond-2753/",
    "hepburnadvocate.com.au": "https://www.realestateview.com.au/for-sale/in-vic-hepburn-3461/",
    "huntervalleynews.net.au": "https://www.realestateview.com.au/for-sale/in-nsw-denman-2328/",
    "illawarramercury.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-wollongong-2500/",
    "inverelltimes.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-inverell-2360/",
    "jimboombatimes.com.au": "https://www.realestateview.com.au/for-sale/in-qld-jimboomba-4280/",
    "juneesoutherncross.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-junee-2663/",
    "katherinetimes.com.au": "https://www.realestateview.com.au/for-sale/in-nt-katherine-0850/",
    "kiamaindependent.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-kiama-2533/",
    "lakesmail.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-morisset-2264/",
    "lithgowmercury.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-lithgow-2790/",
    "liverpoolchampion.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-liverpool-2170/",
    "macarthuradvertiser.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-campbelltown-2560/",
    "macleayargus.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-kempsey-2440/",
    "mailtimes.com.au": "https://www.realestateview.com.au/for-sale/in-vic-horsham-3400/",
    "maitlandmercury.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-maitland-2320/",
    "mandurahmail.com.au": "https://www.realestateview.com.au/for-sale/in-wa-mandurah/",
    "manningrivertimes.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-taree-2430/",
    "margaretrivermail.com.au": "https://www.realestateview.com.au/for-sale/in-wa-augusta-6290/",
    "merimbulanewsweekly.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-merimbula-2548/",
    "moreechampion.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-moree-2400/",
    "mudgeeguardian.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-mudgee-2850/",
    "murrayvalleystandard.com.au": "https://www.realestateview.com.au/for-sale/in-sa-murray-bridge-5253/",
    "muswellbrookchronicle.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-muswellbrook-2333/",
    "nambuccaguardian.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-nambucca-heads-2448/",
    "naracoorteherald.com.au": "https://www.realestateview.com.au/for-sale/in-sa-naracoorte-5271/",
    "naroomanewsonline.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-narromine-2821/",
    "narrominenewsonline.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-narromine-2821/",
    "newcastleherald.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-newcastle-2300/",
    "newcastlestar.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-newcastle-2300/",
    "northernargus.com.au": "https://www.realestateview.com.au/for-sale/in-sa-clarendon-5157/",
    "northerndailyleader.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-tamworth-2340/",
    "northweststar.com.au": "https://www.realestateview.com.au/for-sale/in-qld-mount-isa-4825/",
    "nvi.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-gunnedah-2380/",
    "nynganobserver.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-nyngan-2825/",
    "oberonreview.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-oberon-2787/",
    "parkeschampionpost.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-parkes-2870/",
    "portlincolntimes.com.au": "https://www.realestateview.com.au/for-sale/in-sa-port-lincoln-5606/",
    "portnews.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-port-macquarie-2444/",
    "portpirierecorder.com.au": "https://www.realestateview.com.au/for-sale/in-sa-port-pirie-5540/",
    "portstephensexaminer.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-nelson-bay-2315/",
    "queanbeyanage.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-queanbeyan-2620/",
    "redlandcitybulletin.com.au": "https://www.realestateview.com.au/for-sale/in-qld-redland-bay-4165/",
    "sconeadvocate.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-scone-2337/",
    "singletonargus.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-singleton-2330/",
    "southcoastregister.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-nowra-2541/",
    "southernhighlandnews.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-bowral-2576/",
    "standard.net.au": "https://www.realestateview.com.au/for-sale/in-vic-warrnambool-3280/",
    "stawelltimes.com.au": "https://www.realestateview.com.au/for-sale/in-vic-stawell-3380/",
    "tenterfieldstar.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-tenterfield-2372/",
    "theadvocate.com.au": "https://www.realestateview.com.au/for-sale/in-tas-burnie-7320/",
    "thecourier.com.au": "https://www.realestateview.com.au/for-sale/in-vic-ballarat-3350/",
    "theflindersnews.com.au": "https://www.realestateview.com.au/for-sale/in-sa-karatta-5223/",
    "theislanderonline.com.au": "https://www.realestateview.com.au/for-sale/in-sa-kingscote-5223/",
    "theleader.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-sutherland-2232/",
    "townandcountrymagazine.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-goulburn-2580/",
    "transcontinental.com.au": "https://www.realestateview.com.au/for-sale/in-sa-port-augusta-5700/",
    "ulladullatimes.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-ulladulla-2539/",
    "victorharbortimes.com.au": "https://www.realestateview.com.au/for-sale/in-sa-victor-harbor-5211/",
    "walchanewsonline.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-walcha-2354/",
    "wauchopegazette.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-wauchope-2446/",
    "wellingtontimes.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-wellington-2820/",
    "westcoastsentinel.com.au": "https://www.realestateview.com.au/for-sale/in-sa-ceduna-5690/",
    "westernadvocate.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-bathurst-2795/",
    "westernmagazine.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-dubbo-2830/",
    "whyallanewsonline.com.au": "https://www.realestateview.com.au/for-sale/in-sa-whyalla-5600/",
    "winghamchronicle.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-wingham-2429/",
    "wollondillyadvertiser.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-picton-2571/",
    "yasstribune.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-yass-2582/",
    "youngwitness.com.au": "https://www.realestateview.com.au/for-sale/in-nsw-young-2594/",
}

NAV_SHORTCUTS = [
    {
        "label": "Today’s Paper",
        "description": "Read the latest edition online",
        "url": "/digital-print-edition/",
        "icon": "https://cdn.newsnow.io/XNYkXiBdsC832Jm3ku2C9g/7631980a-da5a-4d97-8b6f-f267a7d78efb.svg",
        "cta": "Read now.",
    },
    {
        "label": "Voice of Real Australia",
        "description": "Discover new people, places and perspectives.",
        "url": "/podcasts/voice-of-real-australia/",
        "icon": "https://cdn.newsnow.io/XNYkXiBdsC832Jm3ku2C9g/ed59eb16-4d1d-4ad4-9522-e59493c1d4ef.jpg",
        "cta": "Listen now.",
    },
    [
        {
            "label": "Interactive Puzzles",
            "description": "Test your skills with Crossword, Sudoku and Ultimate Trivia.",
            "url": "/life-style/puzzles/",
            "icon": "https://cdn.newsnow.io/XNYkXiBdsC832Jm3ku2C9g/d818b395-f698-4c32-9c2e-6435ec1582ab.svg",
            "cta": "Play Puzzles.",
        },
        {
            "label": "Newsletters",
            "description": "Get the latest Breaking News alerts and Daily Headlines straight to your inbox.",
            "url": "/newsletters/",
            "icon": "https://cdn.newsnow.io/XNYkXiBdsC832Jm3ku2C9g/27ab4a38-900d-4dc7-b197-75b5c84ddae8.svg",
            "cta": "Sign up.",
        },
    ],
    {
        "label": "Real Estate View",
        "description": "The trusted source for property.",
        "url_from_rev_map": True,
        "icon": "https://cdn.newsnow.io/XNYkXiBdsC832Jm3ku2C9g/f133f7a4-fabc-40e2-92b0-bfb3ea7ec768.svg",
        "cta": "Visit here.",
    },
]

NAV_SHORTCUT_FIELDS = ["label", "description", "url", "icon"]

NAV_EXTERNAL_LINKS = [
    {
        "icon": "https://cdn.newsnow.io/XNYkXiBdsC832Jm3ku2C9g/efb4daac-5151-4938-85c7-76d82bae4c9f.svg",
        "new_window": True,
        "text": "Explore Travel",
        "url": "https://exploretravel.com.au",
    },
    {
        "icon": "https://cdn.newsnow.io/XNYkXiBdsC832Jm3ku2C9g/0069dc56-dfc0-4e84-b48e-3916a42bd752.svg",
        "new_window": False,
        "text": "CareerOne",
        "url": "https://{domain}/jobs/",
    },
    {
        "icon": "https://cdn.newsnow.io/XNYkXiBdsC832Jm3ku2C9g/8e98b871-207a-46f9-9b41-7ecb8a6e0798.svg",
        "new_window": True,
        "text": "AgTrader",
        "url": "https://www.agtrader.com.au/",
    },
    {
        "icon": "https://cdn.newsnow.io/XNYkXiBdsC832Jm3ku2C9g/4994485c-930c-47da-87b0-3c353051555f.png",
        "new_window": True,
        "text": "Beevo",
        "url": "https://www.beevo.com.au/",
    },
]

NAV_EXTERNAL_LINK_FIELDS = [
    "icon",
    "new_window",
    "text",
    "url",
]

DRIVE_URL = "https://www.drive.com.au/"
DRIVE_HEADING_TEMPLATE = "drive_strap_heading.html"
LEGO_DRIVE_STORY_LIST_TEMPLATE = "drive.html"

EXPLORE_DOMAIN = "www.exploretravel.com.au"

EXTERNAL_STRAP_MAP = {
    "www.thesenior.com.au": {
        "url": "https://www.thesenior.com.au/",
        "template": "the_senior_strap_heading.html",
    },
    "www.theland.com.au": {
        "url": "https://www.theland.com.au/",
        "template": "the_land_strap_heading.html",
    },
    EXPLORE_DOMAIN: {
        "url": "https://www.exploretravel.com.au/",
        "template": "explore_strap_heading.html",
    },
    "www.drive.com.au": {
        "url": DRIVE_URL,
        "template": DRIVE_HEADING_TEMPLATE,
    },
}

AGS_MENU_VISIBLE_URLS = [
    "cattle/dairy",
    "cattle/studstock",
    "cropping",
    "livestock/beef",
    "markets",
    "news",
    "news/business",
    "news/opinion",
    "news/politics",
    "newsletter",
    "on-farm/technology",
    "property",
    "recommended",
    "rural-life",
    "sheep",
    "special-features",
    "subscription",
    "weather",
]

AGS_PRIMARY_NAV_URLS = [
    "cattle/dairy",
    "cattle/studstock",
    "cropping",
    "livestock/beef",
    "markets",
    "news",
    "property",
    "on-farm/technology",
    "sheep",
]

AGS_URLS_NON_MENU_KEEP = ["contact-us", "digital-print-edition"]
AGS_URLS_CREATED_AS_AUTUMN = ["digital-print-edition"]

PAGE_TEMPLATE_CONVERSIONS = [
    {
        "url_regex": re.compile(r"^(advertise|advertising)$"),
        "template": "index_advertisement.html",
    },
    {
        "url_regex": re.compile(r"^classifieds$"),
        "template": "classifieds.html",
    },
    {
        "url_regex": re.compile(r"^gift$"),
        "template": "subscribe_gift_subs_acm.html",
    },
    {
        "url_regex": re.compile(r"^life-style/puzzles$"),
        "template": "puzzle_index.html",
    },
    {
        "url_regex": re.compile(
            r"^puzzles/(crossword|sudoku|ultimate-trivia)$"
        ),
        "template": "puzzle_page.html",
    },
    {
        "url_regex": re.compile(
            r"^(about-us|privacy|digital-print-edition.*)$"
        ),
        "template": "content.html",
    },
    {
        "url_regex": re.compile(r"^contact(-us)?$"),
        "template": "content-two-top-cols.html",
    },
    {
        "url_regex": re.compile(r"^(whats-on/tv-guide|weather(/.*)?)$"),
        "template": "index_page_without_sidebar.html",
    },
    {
        "url_regex": re.compile(r"^subscribe$"),
        "template": lambda instance, _: "subscribe_fullpage_v2.html"
        if instance.is_ct_site
        else "subscribe_fullpage_v2_acm.html",
    },
    {
        "template_regex": re.compile(r"^text\.html$"),
        "template": lambda _, page: page.template,
    },
]

NEW_PAGES = {
    "autumn/header": {
        "name": "Autumn - header",
        "template": "autumn-header.html",
    },
    "autumn/footer": {
        "name": "Autumn - footer",
        "template": "autumn-footer.html",
    },
}

SHARED_PAGE_URLS = [
    "ads.txt",
    "about-us/terms-conditions/digital-subscription",
    "about-us/terms-conditions/newspaper-subscription",
    "advertise",
    "conditions-of-use",
    "coronavirus",
    "podcasts/voice-of-real-australia",
    "privacy-policy",
    "privacy",
]

CONTACT_TOP_LEFT_CONTENT = """<h2>Digital Subscriptions</h2>

<p><a href="tel:1300090805">1300 090 805</a><br />
<a href="mailto:<EMAIL>"><EMAIL></a></p>

<p><strong>Due to COVID-19 we are experiencing extremely high volumes, please contact us via the Help/Chat button at the bottom right of the website. We will endeavour to respond as soon as possible. </strong></p>

<p>Our digital support team is here to help you with anything related to this website. Many common questions can be found in our Help Centre. You can view our digital subscription <a href="/subscribe/">plans here</a>.</p>

<p><a class="button-primary" href="https://australiancommunitymedia.zendesk.com/hc/en-us">Help Centre </a></p>
"""

CONTACT_TOP_RIGHT_CONTENT = """<h2>Newspaper Delivery</h2>

<p><a href="tel:**********">1300 131 095</a><br />
<a href="mailto:<EMAIL>"><EMAIL></a></p>

<p>Our circulation team can help you with anything to do with your local newspaper. We can help with the following details: account management, changing address home delivery.</p>

<p><img alt="" class="icon-left" data-original="{{ static_url }}images/icons/newspaper.png" height="27" width="30" /> Give us a call and we&#39;ll get the paper to you.</p>
"""


def localize_domain(domain):
    is_prod = os.environ.get("DJANGO_ENVIRONMENT") == "production"
    if is_prod:
        return domain
    suffix = os.environ["DJANGO_SUZUKA_URL"].split("/")[2]
    prefix = domain.replace(".", "-")
    return f"{prefix}.{suffix}"


def clean_site_name(name):
    """Remove any Beta[2] suffix from site name"""
    return re.sub(CLEAN_SITE_NAME_REGEX, "", name)


def ask_for_choice(choices, format_func=str):
    """
    List some choices and prompt for the user to choose one.

    Choices can be any iterable.

    Returns the chosen item or `None` if no choices are available.
    """
    count = len(choices)

    if count:
        if count == 1:
            return choices[0]

        # Ask the user to pick from the available options
        while True:
            print("Which one?")

            for i, choice in enumerate(choices):
                print(" %d) %s" % (i + 1, format_func(choice)))

            choice = input("Choice: ")

            try:
                choice = int(choice)
            except ValueError:
                print("Please enter a number")
            else:
                if 1 <= choice <= count:
                    return choices[choice - 1]

                print("Try again")

    return None


def find_story_list(
    site, name, allow_choice=False, create_if_does_not_exist=False
):
    """Try to find a story list by name, and optionally prompt for a choice."""
    site_name = clean_site_name(site.name)

    story_list_title = name.format(site_name)
    try:
        story_list = StoryList.objects.get(
            sites=site,
            title=story_list_title,
        )
    except StoryList.DoesNotExist as ex:
        if allow_choice:
            print(
                "Could not find a story list %s, please choose one"
                % story_list_title
            )
            try:
                story_list = ask_for_choice(
                    StoryList.objects.filter(sites=site)
                )
            except KeyboardInterrupt:
                raise ex
        elif create_if_does_not_exist:
            print('- Creating story list "{}"'.format(story_list_title))

            story_list = StoryList.objects.create(
                title=story_list_title,
                organization=site.settings.organization,
            )
            try:
                # Create story list link to site
                StoryListSite.objects.create(story_list=story_list, site=site)
            except Exception:
                print(
                    '- StoryListSite already exists for story list "{}"'.format(
                        story_list_title
                    )
                )
        else:
            raise StoryList.DoesNotExist(
                "Could not find story list %s" % (story_list_title)
            )
    except StoryList.MultipleObjectsReturned as ex:
        print(
            "More than 1 story list available %s, choosing first one"
            % story_list_title
        )
        try:
            story_list = StoryList.objects.filter(
                sites=site,
                title=story_list_title,
            )[0]
        except KeyboardInterrupt:
            raise ex

    return story_list


def add_classified_item(
    site,
    page,
    zone,
    number_of_items=3,
    template="business_promo.html",
    zone_item=None,
):
    """Add a classified zone item to the given page."""
    if not zone_item:
        zone_item = ZoneItem.objects.create(
            element_type="classified",
            page=page,
            site=site,
            zone=zone,
        )

    Classified.objects.create(
        number_of_items=number_of_items,
        template=template,
        zone_item=zone_item,
    )

    return zone_item


def add_dpelist_item(
    site,
    page,
    zone,
    limit=9,
    dpe_id="",
    template="index.html",
    zone_item=None,
):
    """Add a dpelist zone item to the given page."""
    if not zone_item:
        zone_item = ZoneItem.objects.create(
            element_type="dpelist",
            page=page,
            site=site,
            zone=zone,
        )

    DPEList.objects.create(
        dpe_id=dpe_id,
        limit=limit,
        template=template,
        zone_item=zone_item,
    )

    return zone_item


def add_heading_item(
    site,
    page,
    zone,
    heading,
    url="",
    open_new_window=False,
    template="strap_heading.html",
    zone_item=None,
):
    """Add a heading zone item to the given page."""
    if not zone_item:
        zone_item = ZoneItem.objects.create(
            element_type="heading",
            page=page,
            site=site,
            zone=zone,
        )

    Heading.objects.create(
        heading=heading,
        open_new_window=open_new_window,
        template=template,
        url=url,
        zone_item=zone_item,
    )

    return zone_item


def add_mailing_list_item(
    site,
    page,
    zone,
    template,
    heading_text="",
    text="",
    tags="",
    subscribe_visible=True,
    form_data="",
    marketing_cloud_url="",
):
    """
    Add a mailing list zone item to a page.
    """
    assert template.endswith(".html")

    zone_item = ZoneItem.objects.create(
        element_type="mailinglist",
        page=page,
        site=site,
        zone=zone,
    )

    MailingList.objects.create(
        heading_text=heading_text,
        text=text,
        tags=tags,
        subscribe_visible=subscribe_visible,
        form_data=form_data,
        template=template,
        marketing_cloud_url=marketing_cloud_url,
        zone_item=zone_item,
    )

    return zone_item


def add_menu_list_item(
    site, page, zone, template, menu_list_page, zone_item=None
):
    """
    Add a menu list zone item to a page.
    """
    assert template.endswith(".html")

    if not zone_item:
        zone_item = ZoneItem.objects.create(
            element_type="menulist",
            page=page,
            site=site,
            zone=zone,
        )

    MenuList.objects.create(
        page=menu_list_page,
        template=template,
        zone_item=zone_item,
    )

    return zone_item


def add_story_list_item(
    site,
    page,
    story_list_name,
    zone,
    template,
    limit=None,
    offset=1,
    flip_story_display=True,
    allow_choice=False,
    allow_ads=True,
    is_hero_image=False,
    large_lead_story=False,
    create_storylist_if_does_not_exist=False,
):
    """
    Add a story list zone item to a page.

    `story_list_name` may also be a story list instance.

    Pass `allow_choice=True` to prompt the user to choose a story list if the
    requested one does not exist. Ctrl-C will cancel the choice and raise
    StoryList.DoesNotExist
    """
    assert template.endswith(".html")

    if isinstance(story_list_name, StoryList):
        story_list = story_list_name
    else:
        story_list = find_story_list(
            site,
            story_list_name,
            allow_choice,
            create_storylist_if_does_not_exist,
        )

    story_list_item = ZoneItem.objects.create(
        element_type="storylist",
        page=page,
        site=site,
        zone=zone,
    )

    StoryListElement.objects.create(
        allow_ads=allow_ads,
        flip_story_display=flip_story_display,
        is_hero_image=is_hero_image,
        large_lead_story=large_lead_story,
        limit=limit,
        offset=offset,
        story_list=story_list,
        template=template,
        zone_item=story_list_item,
    )

    return story_list_item


def add_titled_story_list_item(
    site,
    page,
    story_list_name,
    zone,
    template,
    title,
    limit=None,
    allow_choice=False,
    allow_ads=True,
    create_storylist_if_does_not_exist=False,
):
    """
    Add a titled story list zone item to a page.

    `story_list_name` may also be a story list instance.

    Pass `allow_choice=True` to prompt the user to choose a story list if the
    requested one does not exist. Ctrl-C will cancel the choice and raise
    StoryList.DoesNotExist
    """
    assert template.endswith(".html")

    if isinstance(story_list_name, StoryList):
        story_list = story_list_name
    else:
        story_list = find_story_list(
            site,
            story_list_name,
            allow_choice,
            create_storylist_if_does_not_exist,
        )

    story_list_item = ZoneItem.objects.create(
        element_type="titledstorylist",
        page=page,
        site=site,
        zone=zone,
    )

    TitledStoryList.objects.create(
        allow_ads=allow_ads,
        limit=limit,
        story_list=story_list,
        template=template,
        title=title,
        zone_item=story_list_item,
    )

    return story_list_item


def add_clustered_story_list_item(
    site,
    page,
    story_list_name,
    zone,
    template,
    from_organisation,
    limit=None,
    allow_choice=False,
    create_storylist_if_does_not_exist=False,
):
    """
    Add a titled story list zone item to a page.

    `story_list_name` may also be a story list instance.

    Pass `allow_choice=True` to prompt the user to choose a story list if the
    requested one does not exist. Ctrl-C will cancel the choice and raise
    StoryList.DoesNotExist
    """
    assert template.endswith(".html")

    if isinstance(story_list_name, StoryList):
        story_list = story_list_name
    else:
        story_list = find_story_list(
            site,
            story_list_name,
            allow_choice,
            create_storylist_if_does_not_exist,
        )

    story_list_item = ZoneItem.objects.create(
        element_type="clusteredstorylist",
        page=page,
        site=site,
        zone=zone,
    )

    ClusteredStoryList.objects.create(
        limit=limit,
        story_list=story_list,
        template=template,
        from_organisation=from_organisation,
        zone_item=story_list_item,
    )

    return story_list_item


def add_nav_item(site, page, nav_fields, zone=MAIN_ZONE):
    """Add a nav zone item to the given page."""
    zone_item = ZoneItem.objects.create(
        element_type="navigation",
        page=page,
        site=site,
        zone=zone,
    )

    Navigation.objects.create(
        zone_item=zone_item,
        **nav_fields,
    )

    return zone_item


def get_best_fit_zone_story_list(zone_items, title):
    try:
        return [
            zone_item
            for zone_item in zone_items
            if (
                zone_item.element_type == "storylist"
                and zone_item.storylist
                and title in zone_item.storylist.story_list.title
            )
        ][0].storylist.story_list
    except IndexError:
        pass


def log_prefix(no_update):
    return ("\u2022" if no_update else "=") + " "


def get_equiv_prod_domain(domain):
    return (
        domain
        if domain.startswith(("www.", "beta."))
        else domain.split(".")[0].replace("-", ".")
    )


class SiteTransformer:
    def __init__(self, cmd, site, dry_run=True, verbose=False):
        self.cmd = cmd
        self.site = site
        self.dry_run = dry_run
        self.verbose = verbose

        try:
            self.site_org_name = [
                o.name
                for o in shared_orgs_api.orgs()
                if o.pk == int(self.site.settings.organization)
            ][0]
        except IndexError:
            self.log(
                f"WARNING: Can't find site org for id {self.site.settings.organization}"
            )
        else:
            self.log(f"Using site org name: {self.site_org_name}")

        self.equiv_prod_domain = get_equiv_prod_domain(self.site.domain)
        self.domain = self.equiv_prod_domain.partition(".")[2]
        self.is_prod = os.environ.get("DJANGO_ENVIRONMENT") == "production"
        self.is_autumn = self.site.settings.theme_dir == "autumn"
        self.is_lego = self.site.settings.theme_dir == "legolite"
        self.is_ags = self.site.settings.theme_dir == "krypton"
        self.is_daily_site = self.domain in DAILY_DOMAINS
        self.is_ct_site = self.domain == "canberratimes.com.au"
        self.publication = clean_site_name(self.site.name)
        self.local_news_story_list_title = f"{self.publication} Local News"
        self.local_news_story_list = None

        self.created_page_ids = []
        self.pages = site.page_set.all()
        self.zone_items = site.zoneitem_set.all()

        self.log(f"DOMAIN: {self.domain}")
        self.log(f"THEME: {self.site.settings.theme_dir}")
        self.log(f"DAILY: {self.is_daily_site}")
        self.log(f"IS CT: {self.is_ct_site}")

    def log(self, text, no_update=None):
        prefix = log_prefix(no_update) if no_update is not None else ""
        if self.verbose or no_update is not True:
            self.cmd.stdout.write(f"{prefix}{text}")

    def delete_zone_item(self, zone_item):
        self.log(
            f"Deleting {zone_item}",
            False,
        )
        if not self.dry_run:
            zone_item.delete()

    def get_puzzle_links_for_settings(self, attrib_updates):
        puzzles = {
            "puzzles/crossword": "puzzlefeature_crossword",
            "puzzles/sudoku": "puzzlefeature_sudoku",
            "puzzles/ultimate-trivia": "puzzlefeature_ultimate_trivia",
        }

        for url, attrib in puzzles.items():
            try:
                page = self.pages.get(url=url)
            except Page.DoesNotExist:
                if self.is_daily_site:
                    self.log(f"WARNING: Puzzle page not found for url: {url}")
            else:
                zone_items = page.zone_items.filter(
                    zone=MAIN_ZONE, element_type="iframe"
                ).order_by("order")

                for zone_item in zone_items:
                    try:
                        if zone_item.iframe.url.startswith(
                            "https://data.puzzlexperts.com"
                        ):
                            attrib_updates[attrib] = zone_item.iframe.url
                            break
                    except models.ObjectDoesNotExist as e:
                        self.log(f"WARNING: {e} For {zone_item}")

    def update_settings(self):
        self.log("Settings:")
        attrib_updates = {
            "theme_dir": THEME,
            "use_suzuka_ui": True,
        }

        features_attrib_updates = {}

        # Ad Serving
        features_attrib_updates["adservingfeature_enabled"] = True
        attrib_updates["adservingfeature_fetch_margin_percent"] = (
            FETCH_MARGIN_PERCENT
        )
        attrib_updates["adservingfeature_mobile_scaling"] = MOBILE_SCALING
        attrib_updates["adservingfeature_render_margin_percent"] = (
            RENDER_MARGIN_PERCENT
        )

        # Always enable DPE:
        dpe_previously_enabled = self.site.settings.features.dpefeature_enabled
        features_attrib_updates["dpefeature_enabled"] = True
        attrib_updates["dpefeature_version"] = "v2"
        try:
            dpe_id = DOMAIN_CONFIG[self.domain].get("dpe_id", "")
        except KeyError:
            dpe_id = ""

        if not dpe_previously_enabled:
            # Don't overwrite these values if DPE was previously enabled
            attrib_updates["dpefeature_dpe_publish_time"] = "0400"
            attrib_updates["dpefeature_dpe_id"] = dpe_id

        if self.is_lego:
            self.get_puzzle_links_for_settings(attrib_updates)

        self.update_instance_attribs(
            self.site.settings.features, features_attrib_updates
        )
        self.update_instance_attribs(self.site.settings, attrib_updates)

    def update_site(self):
        self.log("Site:")
        attrib_updates = {
            "name": f"zBeta {self.publication}",
        }
        self.update_instance_attribs(self.site.settings, attrib_updates)

    def get_page(self, url):
        try:
            page = self.pages.get(url=url)
            return page
        except Page.DoesNotExist:
            self.log(f"WARNING: Page not found for url: {url}")

    def page_template_conversion(self, page, is_primary_page):
        keys = {"url_regex": "url", "template_regex": "template"}

        for conversion in PAGE_TEMPLATE_CONVERSIONS:
            for key, attrib in keys.items():
                regex = conversion.get(key)
                if regex:
                    if re.match(regex, getattr(page, attrib, "")):
                        template = conversion.get("template")
                        if template is not None:
                            return (
                                template(self, page)
                                if callable(template)
                                else template
                            )

        return (
            PRIMARY_INDEX_PAGE_TEMPLATE
            if is_primary_page
            else SECONDARY_INDEX_PAGE_TEMPLATE
        )

    def update_homepage(self):
        page = self.get_page("/")
        if not page:
            return

        self.log(f"Page: [{page.id}] {page.url} {page}")

        self.update_instance_attribs(
            page,
            {
                "name": "Home Page",
                "menu_visible": True,
                "template": "one_column.html",
            },
        )

        self.update_homepage_zone_items(page)

    def get_local_news_story_list(self):
        if self.local_news_story_list:
            return self.local_news_story_list

        self.local_news_story_list = find_story_list(
            self.site, self.local_news_story_list_title
        )

        self.log(
            f"INFO: Found Local News story list: {self.local_news_story_list}"
        )

        return self.local_news_story_list

    def update_homepage_newswell_zone_items(self, page):
        zone = NEWS_WELL_ZONE
        self.log(f"Zone: {zone}")

        if self.is_ags:
            self.log("skipping", True)
            return

        zone_items = page.zone_items.filter(
            zone=zone,
        ).order_by("order")

        # Find best fit Local News storylist (to be used by rest of process also)
        # This is probably only an exception for NCH on its homepage.

        local_news_story_list = get_best_fit_zone_story_list(
            zone_items, self.local_news_story_list_title
        )

        if not local_news_story_list:
            local_news_story_list = self.get_local_news_story_list()

        self.log(
            f"Using Local News story list: [{local_news_story_list.id}] {local_news_story_list.title}"
        )

        # Gut all newswell zone items

        self.log(
            f"Deleting all ({zone_items.count()}) {zone} zoneitems", False
        )
        if not self.dry_run:
            zone_items.delete()

        # Create newswell zone items (all storylists)

        new_zone_items = [
            {
                "flip_story_display": False,
                "limit": 4,
                "offset": 1,
                "story_list_name": local_news_story_list,
                "is_hero_image": True,
                "large_lead_story": True,
            },
            {
                "flip_story_display": False,
                "limit": 1,
                "offset": 1,
                "story_list_name": f"{self.publication} Video",
            },
            {
                "flip_story_display": False,
                "limit": 2,
                "offset": 1,
                "story_list_name": "National 9676",
            },
            {
                "flip_story_display": True,
                "limit": 16,
                "offset": 5,
                "story_list_name": local_news_story_list,
            },
        ]

        self.log(f"Creating {zone} zone items")

        for item in new_zone_items:
            story_list_title = item["story_list_name"]
            self.log(
                f"Creating storylist with args {item}",
                False,
            )
            if not self.dry_run:
                try:
                    story_list_zone_item = add_story_list_item(
                        self.site,
                        page,
                        story_list_title,
                        zone,
                        NEWSWELL_STORY_LIST_TEMPLATE,
                        limit=item["limit"],
                        offset=item["offset"],
                        flip_story_display=item["flip_story_display"],
                        is_hero_image=item.get("is_hero_image", False),
                        large_lead_story=item.get("large_lead_story", False),
                    )
                except StoryList.DoesNotExist:
                    self.log(
                        f"WARNING: Story list not found: {story_list_title}"
                    )
                else:
                    self.log(
                        f"Created {zone} storylist zone item: [{story_list_zone_item.id}] {story_list_zone_item}",
                        False,
                    )
            else:
                # For dry run, at least check if storylist can be found
                if isinstance(story_list_title, str):
                    try:
                        story_list = find_story_list(
                            self.site, story_list_title
                        )
                    except StoryList.DoesNotExist:
                        self.log(
                            f"WARNING: Story list not found: {story_list_title}"
                        )
                    else:
                        self.log(
                            f"Found story list: [{story_list.id}] {story_list}"
                        )

    def update_homepage_newswell_side_zone_items(self, page):
        zone = NEWS_WELL_SIDE_ZONE
        self.log(f"Zone: {zone}")

        if self.is_ags:
            self.log("skipping", True)
            return

        zone_items = page.zone_items.filter(
            zone=zone,
        ).order_by("order")

        # Gut all newswell-side zone items

        self.log(
            f"Deleting all ({zone_items.count()}) {zone} zone items", False
        )
        if not self.dry_run:
            zone_items.delete()

        self.log(f"Creating {zone} nav strap", False)
        navigation = {}
        self.update_nav_shortcuts(navigation, is_strap=True)
        if not self.dry_run:
            add_nav_item(self.site, page, navigation, zone)

        self.log(f"Creating {zone} title story lists", False)

        new_zone_items = [
            (
                {
                    "limit": 5,
                    "story_list_name": [
                        "Latest News",
                        "{publication} Latest News",
                    ],
                    "title": "Latest News",
                    "type": "titled",
                }
                if self.is_daily_site
                else {
                    "limit": 5,
                    "story_list_name": self.get_local_news_story_list(),
                    "from_organisation": self.site_org_name,
                    "type": "clustered",
                }
            )
        ]

        for item in new_zone_items:
            self.log(
                f"Creating storylist with args {item}",
                False,
            )
            title = item["story_list_name"]
            story_list_titles = title if isinstance(title, list) else [title]

            for story_list_title in story_list_titles:
                final_story_list_title = (
                    story_list_title.format(publication=self.publication)
                    if isinstance(story_list_title, str)
                    else story_list_title
                )
                if not self.dry_run:
                    try:
                        if item["type"] == "titled":
                            story_list_zone_item = add_titled_story_list_item(
                                self.site,
                                page,
                                final_story_list_title,
                                zone,
                                NEWSWELL_SIDE_STORY_LIST_TEMPLATE,
                                limit=item["limit"],
                                title=item["title"],
                            )
                        else:
                            story_list_zone_item = (
                                add_clustered_story_list_item(
                                    self.site,
                                    page,
                                    final_story_list_title,
                                    zone,
                                    NEWSWELL_SIDE_STORY_LIST_TEMPLATE,
                                    limit=item["limit"],
                                    from_organisation=item[
                                        "from_organisation"
                                    ],
                                )
                            )
                    except StoryList.DoesNotExist:
                        self.log(
                            f"WARNING: Story list not found: {final_story_list_title}"
                        )
                    else:
                        self.log(
                            f"Created {zone} titled storylist zone item: [{story_list_zone_item.id}] {story_list_zone_item}"
                        )
                        break
                else:
                    # For dry run, at least check if storylist can be found
                    if isinstance(final_story_list_title, str):
                        try:
                            story_list = find_story_list(
                                self.site, final_story_list_title
                            )
                        except StoryList.DoesNotExist:
                            self.log(
                                f"WARNING: Story list not found: {final_story_list_title}"
                            )
                        else:
                            self.log(
                                f"Found story list: [{story_list.id}] {story_list}"
                            )
                            break
            else:
                self.log(
                    f"WARNING: Skipping storylist item as storylist title(s) not found: {item}"
                )

            # Add mailing list
            self.log(
                "Creating mailing list",
                False,
            )

            if not self.dry_run:
                support_text = "Sign up to receive our Breaking News Alerts and Daily Headlines featuring the best local news and stories."
                add_mailing_list_item(
                    self.site,
                    page,
                    zone,
                    NEWSWELL_SIDE_MAILING_LIST_TEMPLATE,
                    text=support_text,
                )

    def update_instance_attrib(self, instance, attr_name, value, save=False):
        is_same = getattr(instance, attr_name) == value
        if not is_same:
            setattr(instance, attr_name, value)
            if save and not self.dry_run:
                instance.save()
        self.log(
            f"{attr_name}: {value}",
            is_same,
        )
        return not is_same

    def update_instance_attribs(self, instance, attrib_values, save=True):
        if not attrib_values:
            return False

        have_updates = False

        for attrib, value in attrib_values.items():
            have_updates |= self.update_instance_attrib(
                instance, attrib, value
            )

        if have_updates and not self.dry_run and save:
            instance.save()

        return have_updates

    def update_codesnippet(
        self, page, zone, zone_item, found_strap_domains=None
    ):
        code = zone_item.codesnippet.code
        soup = BeautifulSoup(code, features="html5lib")

        # NOTE: REV strap will have 2 anchors. The first should be the local page link.
        # In that case, we don't care about the 2nd href.
        # All other codesnippet heading straps just have a single href as far as I can tell.

        heading = (
            soup.find("h3", {"class": "zone-heading__title"})
            if self.is_ags
            else soup.find("h2", {"class": "heading"})
        )

        if heading:
            anchor = heading.find("a")
            if anchor:
                href = anchor.get("href")
                if href:
                    # Check if external link or internal page
                    # NOTE: going to assume always prod www links for internal page detection

                    own_domain = f"https://www.{self.domain}"

                    if href.startswith(("/", own_domain)):
                        # TODO: Not catering for "//" prefix or http

                        # Find local page (intend to convert to Menulist zone item)
                        path = (
                            href if href[0] == "/" else href[len(own_domain) :]
                        ).strip("/")
                        try:
                            menu_list_page = self.pages.get(url=path)
                        except Page.DoesNotExist:
                            self.log(
                                f"WARNING: Can't find page for path '{path}' in codesnippet {zone_item}"
                            )
                        else:
                            self.log(
                                f"Replaced with menulist element: page: {menu_list_page}",
                                False,
                            )
                            if not self.dry_run:
                                # Note: We re-use the same zone item, deleting codesnippet element
                                # and replace with new menulist element.

                                code_snippet = zone_item.codesnippet
                                zone_item.element_type = "menulist"
                                zone_item.save()

                                # NOTE: I think it might be important to delete after the save otherwise
                                # there might be ordering issue.
                                code_snippet.delete()

                                menu_list_template = (
                                    REV_STRAP_ZONE_ITEM_TEMPLATE
                                    if not self.is_ags
                                    and menu_list_page.name == "Property"
                                    else MENU_LIST_TEMPLATE
                                )

                                add_menu_list_item(
                                    self.site,
                                    page,
                                    zone_item.zone,
                                    menu_list_template,
                                    menu_list_page,
                                    zone_item=zone_item,
                                )
                            else:
                                # Hack to help other zone items checks see replaced values in dry run.
                                zone_item.element_type = "menulist"
                            return
                    else:
                        # External page (intend to convert to Heading zone item)
                        template = CUSTOM_HEADING_TEMPLATE
                        heading = ""

                        for domain, value in EXTERNAL_STRAP_MAP.items():
                            if domain in href:
                                template = value["template"]
                                heading = "Stories from"
                                if isinstance(found_strap_domains, list):
                                    found_strap_domains.append(domain)
                                break

                        self.log(
                            f"Replaced with heading element: heading: '{heading}' url: {href}, open_new_window: True",
                            False,
                        )
                        if not self.dry_run:
                            # Note: We re-use the same zone item, deleting codesnippet element
                            # and replace with new heading element.
                            code_snippet = zone_item.codesnippet
                            zone_item.element_type = "heading"
                            zone_item.save()

                            # NOTE: I think it might be important to delete after the save otherwise
                            # there might be ordering issue.
                            code_snippet.delete()

                            add_heading_item(
                                self.site,
                                page,
                                zone,
                                heading,
                                url=href,
                                open_new_window=True,
                                template=template,
                                zone_item=zone_item,
                            )
                        else:
                            # Hack to help other zone items checks see replaced values in dry run.
                            zone_item.element_type = "heading"
                        return

        # Non-heading code snippet (or fell thru not maching heading properly)

        attrib_updates = {
            "code": code.replace("data-src", "data-original"),
        }
        if "content" in page.template:
            attrib_updates["template"] = "standard.html"

        self.update_instance_attribs(zone_item.codesnippet, attrib_updates)

    def create_drive_heading(
        self, page, idx, zone_item, zone_items, zone_item_moves
    ):
        # Add heading before this zone item (if not already there)
        try:
            if (
                idx == 0
                or zone_items[idx - 1].element_type != "heading"
                or zone_items[idx - 1].heading.url != DRIVE_URL
            ):
                self.log("Add heading zone item for DRIVE", False)
                if not self.dry_run:
                    heading_zone_item = add_heading_item(
                        self.site,
                        page,
                        zone_item.zone,
                        "Stories from",
                        url=DRIVE_URL,
                        open_new_window=True,
                        template=DRIVE_HEADING_TEMPLATE,
                    )
                    self.add_zone_item_move(
                        zone_item_moves,
                        heading_zone_item.id,
                        zone_item.id,
                    )
            else:
                self.log(
                    "INFO: DRIVE heading already present for DRIVE storylist.",
                    False,
                )
        except Heading.DoesNotExist:
            self.log(
                f"WARNING: Previous heading zoneitem not linked to element: {zone_items[idx - 1]}"
            )

    def create_explore_strap(self, page, zone):
        strap = EXTERNAL_STRAP_MAP[EXPLORE_DOMAIN]

        self.log("Add heading zone item for EXPLORE", False)
        if not self.dry_run:
            add_heading_item(
                self.site,
                page,
                zone,
                "Stories from",
                url=strap["url"],
                open_new_window=True,
                template=strap["template"],
            )

        story_list_title = "Regional Traveller"
        self.log("Add storylist zone item for EXPLORE", False)
        if not self.dry_run:
            try:
                add_story_list_item(
                    self.site,
                    page,
                    story_list_title,
                    zone,
                    EXPLORE_STRAP_TEMPLATE,
                    limit=8,
                )
            except StoryList.DoesNotExist:
                self.log(
                    f"WARNING: Zone item not added as can't find storylist: {story_list_title}"
                )

    def create_realestateview_menulist(
        self, page, idx, zone_item, zone_items, zone_item_moves
    ):
        try:
            menu_list_page = self.pages.get(name="Property")
        except Page.DoesNotExist:
            self.log(
                "WARNING: Can't add REV menulist as can't find 'Property' page."
            )
        else:
            # Add menulist to Property page before this zone item (if not already there)
            try:
                if (
                    idx == 0
                    or zone_items[idx - 1].element_type != "menulist"
                    or zone_items[idx - 1].menulist.page != menu_list_page
                ):
                    self.log("Add menulist zone item for REV", False)
                    if not self.dry_run:
                        menu_list_zone_item = add_menu_list_item(
                            self.site,
                            page,
                            zone_item.zone,
                            REV_STRAP_ZONE_ITEM_TEMPLATE,
                            menu_list_page,
                        )
                        self.add_zone_item_move(
                            zone_item_moves,
                            menu_list_zone_item.id,
                            zone_item.id,
                        )
                else:
                    self.log(
                        "INFO: Property menulist already present for REV storylist.",
                        False,
                    )
            except MenuList.DoesNotExist:
                self.log(
                    f"WARNING: Previous menulist zoneitem not linked to page: {zone_items[idx - 1]}"
                )

    def update_homepage_main_zone_items(self, page):
        zone = MAIN_ZONE
        self.log(f"Zone: {zone}")

        zone_items = page.zone_items.filter(
            zone=zone,
        ).order_by("order")

        zone_item_moves = []
        found_strap_domains = []

        for idx, zone_item in enumerate(zone_items):
            delete_zone_item = False
            try:
                self.log(
                    f"Zone Item: [{zone_item.id}] {zone_item.element_type}"
                )

                if zone_item.element_type == "advertisement":
                    delete_zone_item = True

                elif zone_item.element_type == "codesnippet":
                    if self.is_ags:
                        if "zone-heading" in zone_item.codesnippet.code:
                            self.update_codesnippet(page, zone, zone_item)
                        elif (
                            "twelve columns alignc"
                            in zone_item.codesnippet.code
                        ):
                            pass
                        else:
                            delete_zone_item = True
                    else:
                        self.update_codesnippet(
                            page, zone, zone_item, found_strap_domains
                        )

                elif zone_item.element_type == "classified":
                    delete_zone_item = True

                elif zone_item.element_type == "heading":
                    attrib_updates = {}

                    if zone_item.heading.template == "strap_heading.html":
                        attrib_updates["template"] = CUSTOM_HEADING_TEMPLATE

                    # Just making sure external strap heading zone items have correct template
                    for domain, value in EXTERNAL_STRAP_MAP.items():
                        if domain in zone_item.heading.url:
                            if not value.get("delete"):
                                attrib_updates["url"] = value["url"]
                                attrib_updates["template"] = value["template"]
                            break

                    self.update_instance_attribs(
                        zone_item.heading, attrib_updates
                    )

                elif zone_item.element_type == "mailinglist":
                    delete_zone_item = True

                elif zone_item.element_type == "menulist":
                    if (
                        zone_item.menulist.page
                        and zone_item.menulist.page.url
                        == "digital-print-edition"
                    ):
                        delete_zone_item = True
                    else:
                        self.update_instance_attrib(
                            zone_item.menulist,
                            "template",
                            MENU_LIST_TEMPLATE,
                            save=True,
                        )

                elif zone_item.element_type == "storylist":
                    flip_story_display = False
                    offset = 1
                    allow_ads = True

                    if self.is_ags:
                        template = STORY_LIST_MAIN_TEMPLATE
                        limit = 3
                    else:
                        if (
                            zone_item.storylist.story_list
                            and "Property"
                            in zone_item.storylist.story_list.title
                            and zone_item.storylist.template
                            == "strap-1-1-1-separator.html"
                        ):
                            limit = 3
                        else:
                            try:
                                limit = MAIN_STORY_LIST_ZONE_TEMPLATE_LIMITS[
                                    zone_item.storylist.template
                                ]
                            except KeyError:
                                # Don't change limit if already on autumn main template
                                limit = (
                                    zone_item.storylist.limit
                                    if zone_item.storylist.template
                                    == STORY_LIST_MAIN_TEMPLATE
                                    else 5
                                )
                        if (
                            zone_item.storylist.template
                            == LEGO_DRIVE_STORY_LIST_TEMPLATE
                        ):
                            template = LEGO_DRIVE_STORY_LIST_TEMPLATE
                            self.create_drive_heading(
                                page,
                                idx,
                                zone_item,
                                zone_items,
                                zone_item_moves,
                            )
                        elif (
                            zone_item.storylist.template
                            == LEGO_REALESTATE_VIEW_TEMPLATE
                        ):
                            template = REALESTATE_VIEW_TEMPLATE
                            self.create_realestateview_menulist(
                                page,
                                idx,
                                zone_item,
                                zone_items,
                                zone_item_moves,
                            )
                        else:
                            template = STORY_LIST_MAIN_TEMPLATE

                    self.update_instance_attribs(
                        zone_item.storylist,
                        {
                            "limit": limit,
                            "offset": offset,
                            "template": template,
                            "flip_story_display": flip_story_display,
                            "allow_ads": allow_ads,
                        },
                    )

                elif zone_item.element_type == "textblock":
                    if zone_item.textblock.template not in [
                        "text1.html",
                        "text2.html",
                    ]:
                        self.update_instance_attribs(
                            zone_item.textblock,
                            {
                                "template": "text1.html",
                            },
                        )

                else:
                    self.log(f"WARNING: skipping {zone_item}")

                if delete_zone_item:
                    self.delete_zone_item(zone_item)

            except models.ObjectDoesNotExist as e:
                self.log(f"WARNING: {e} For {zone_item}")

        # If explore strap was not found, add it

        if EXPLORE_DOMAIN not in found_strap_domains:
            self.create_explore_strap(page, zone)

        self.move_zone_items(zone_item_moves, zone_items)

    def update_homepage_main_side_zone_items(self, page):
        zone = MAIN_SIDE_ZONE
        self.log(f"Zone: {zone}")

        if self.is_ags:
            self.log("skipping", True)
            return

        zone_items = page.zone_items.filter(
            zone=zone,
        ).order_by("order")

        # Gut all zone items

        self.log(
            f"Deleting all ({zone_items.count()}) {zone} zone items", False
        )
        if not self.dry_run:
            zone_items.delete()

        add_classified_item(
            self.site,
            page,
            zone,
        )

    def add_zone_item_move(self, moves, from_id, to_id):
        moves.append(
            {
                "from_id": from_id,
                "to_id": to_id,
            }
        )

    def move_zone_items(self, moves, zone_items):
        for move in moves:
            self.move_zone_item(
                zone_items.all(), move["from_id"], move["to_id"]
            )

    def move_zone_item(self, zone_items, from_zone_item_id, to_zone_item_id):
        try:
            from_zone_item = ZoneItem.objects.get(id=from_zone_item_id)
        except ZoneItem.DoesNotExist:
            self.log(
                f"WARNING: Failed zone item move from {from_zone_item_id} to {to_zone_item_id} as {from_zone_item_id} not found."
            )
            return

        try:
            to_zone_item = ZoneItem.objects.get(id=to_zone_item_id)
        except ZoneItem.DoesNotExist:
            self.log(
                f"WARNING: Failed zone item move from {from_zone_item_id} to {to_zone_item_id} as {to_zone_item_id} not found."
            )
            return

        old_position = from_zone_item.order
        dest_position = to_zone_item.order

        self.log(
            f"Moving zone item [{from_zone_item_id}] {from_zone_item.element_type} from position {old_position} to {dest_position}"
        )

        if old_position > dest_position:
            direction = 1
            filters = {
                "order__lt": old_position,
                "order__gte": dest_position,
            }
        else:
            direction = -1
            filters = {
                "order__gt": old_position,
                "order__lte": dest_position,
            }

        misordered_zone_items = zone_items.filter(**filters)

        # Reorder zone items between old position and new position.
        self.log("Zone items to be re-ordered:")

        for zi in misordered_zone_items:
            self.log(
                f"[{zi.id}] {zi.element_type} order {zi.order} -> {zi.order + direction}"
            )
            zi.order += direction
            if not self.dry_run:
                zi.save()

        from_zone_item.order = dest_position
        if not self.dry_run:
            from_zone_item.save()

    def update_index_page_main_zone_items(self, page, is_primary_page):
        zone = MAIN_ZONE
        zone_items = page.zone_items.filter(
            zone=zone,
        ).order_by("order")

        self.log(f"Zone: {zone}")

        if self.is_lego and page.url in [
            "life-style/puzzles",
            "puzzles/crossword",
            "puzzles/sudoku",
            "puzzles/ultimate-trivia",
        ]:
            self.log(f"Removing all ({zone_items.count()}) zone items")
            if not self.dry_run:
                zone_items.delete()
            return

        zone_item_moves = []
        found_list_view_story_list = False
        found_scores_and_draws_story_list = False
        story_list_count = 0
        for idx, zone_item in enumerate(zone_items):
            delete_zone_item = False
            try:
                self.log(
                    f"Zone Item [{zone_item.id}]: {zone_item.element_type}"
                )

                if zone_item.element_type == "advertisement":
                    delete_zone_item = True

                elif zone_item.element_type == "codesnippet":
                    if self.is_ags:
                        if "zone-heading" in zone_item.codesnippet.code:
                            self.update_codesnippet(page, zone, zone_item)
                        elif (
                            "twelve columns alignc"
                            in zone_item.codesnippet.code
                        ):
                            pass
                        else:
                            delete_zone_item = True
                    else:
                        self.update_codesnippet(page, zone, zone_item)

                if zone_item.element_type == "heading":
                    attrib_updates = {}

                    if (
                        zone_item.heading.template == "strap_heading.html"
                        or page.url in ["contact", "contact-us"]
                    ):
                        attrib_updates["template"] = CUSTOM_HEADING_TEMPLATE

                    # Just making sure external strap heading zone items have correct template
                    for domain, value in EXTERNAL_STRAP_MAP.items():
                        if domain in zone_item.heading.url:
                            if not value.get("delete"):
                                attrib_updates["template"] = value["url"]
                            break

                    self.update_instance_attribs(
                        zone_item.heading, attrib_updates
                    )

                elif zone_item.element_type == "dpelist":
                    if page.url.startswith("digital-print-edition"):
                        self.update_instance_attribs(
                            zone_item.dpelist,
                            {
                                "limit": 10,
                                "template": "index.html",
                            },
                        )

                elif zone_item.element_type == "mailinglist":
                    if page.url.startswith("digital-print-edition"):
                        template = "today_paper_alert.html"
                        self.update_instance_attribs(
                            zone_item.mailinglist,
                            {
                                "template": template,
                            },
                        )
                elif zone_item.element_type == "menulist":
                    if self.is_ags:
                        self.update_instance_attrib(
                            zone_item.menulist,
                            "template",
                            MENU_LIST_TEMPLATE,
                            save=True,
                        )

                elif zone_item.element_type == "storylist":
                    story_list_count += 1
                    flip_story_display = False
                    offset = 1
                    allow_ads = True

                    if self.is_ags:
                        template = STORY_LIST_MAIN_TEMPLATE
                        limit = 3
                    else:
                        if (
                            page.url == "scores-and-draws"
                            and zone_item.storylist.template
                            == SCORES_AND_DRAWS_TEMPLATE
                        ):
                            self.log(
                                f"Found Scores and Draws story list zone item in {zone} zone"
                            )
                            found_scores_and_draws_story_list = True
                            continue

                        if (
                            not is_primary_page
                            and page.story_list
                            and zone_item.storylist.template
                            == LIST_VIEW_LOAD_MORE_TEMPLATE
                        ):
                            self.log(
                                f"Found list view story list zone item in {zone} zone"
                            )
                            found_list_view_story_list = True
                            continue

                        if zone_item.storylist.template in ["home-cards.html"]:
                            template = LIST_VIEW_LOAD_MORE_TEMPLATE
                            offset = 1
                            allow_ads = False
                            limit = 10
                        elif (
                            zone_item.storylist.template
                            == LEGO_DRIVE_STORY_LIST_TEMPLATE
                        ):
                            template = LEGO_DRIVE_STORY_LIST_TEMPLATE
                            self.create_drive_heading(
                                page,
                                idx,
                                zone_item,
                                zone_items,
                                zone_item_moves,
                            )
                        elif (
                            zone_item.storylist.template
                            == LEGO_REALESTATE_VIEW_TEMPLATE
                        ):
                            template = REALESTATE_VIEW_TEMPLATE
                            self.create_realestateview_menulist(
                                page,
                                idx,
                                zone_item,
                                zone_items,
                                zone_item_moves,
                            )
                        else:
                            if (
                                page.url == "recommended"
                                and "Features"
                                in zone_item.storylist.story_list.title
                            ):
                                template = BUSINESS_FEATURE_TEMPLATE
                            else:
                                template = STORY_LIST_MAIN_TEMPLATE

                            if (
                                is_primary_page
                                and story_list_count == 1
                                and page.url not in ["recommended"]
                            ):
                                limit = 8
                            else:
                                try:
                                    limit = (
                                        MAIN_STORY_LIST_ZONE_TEMPLATE_LIMITS[
                                            zone_item.storylist.template
                                        ]
                                    )
                                except KeyError:
                                    # Don't change limit if already on autumn main template
                                    limit = (
                                        zone_item.storylist.limit
                                        if zone_item.storylist.template
                                        == STORY_LIST_MAIN_TEMPLATE
                                        else 5
                                    )

                    self.update_instance_attribs(
                        zone_item.storylist,
                        {
                            "limit": limit,
                            "offset": offset,
                            "template": template,
                            "flip_story_display": flip_story_display,
                            "allow_ads": allow_ads,
                        },
                    )

                elif zone_item.element_type == "textblock":
                    if zone_item.textblock.template not in [
                        "text1.html",
                        "text2.html",
                    ]:
                        self.update_instance_attribs(
                            zone_item.textblock,
                            {
                                "template": "text1.html",
                            },
                        )

                elif zone_item.element_type == "weather":
                    self.update_instance_attribs(
                        zone_item.weather,
                        {
                            "template": WEATHER_ZONE_TEMPLATE,
                        },
                    )

                else:
                    self.log(f"WARNING: skipping {zone_item}")

                if delete_zone_item:
                    self.delete_zone_item(zone_item)

            except models.ObjectDoesNotExist as e:
                self.log(f"WARNING: {e} For {zone_item}")

        self.move_zone_items(zone_item_moves, zone_items)

        if not is_primary_page and not found_list_view_story_list:
            if page.story_list:
                self.log(
                    f"Creating storylist with page storylist '{page.story_list}' and {LIST_VIEW_LOAD_MORE_TEMPLATE} template"
                )
                if not self.dry_run:
                    add_story_list_item(
                        self.site,
                        page,
                        page.story_list,
                        zone,
                        LIST_VIEW_LOAD_MORE_TEMPLATE,
                        limit=8 if self.is_ags else 10,
                    )

        if (
            page.url == "scores-and-draws"
            and not found_scores_and_draws_story_list
        ):
            self.log(
                f"Creating storylist 'Scores and Draws' with {SCORES_AND_DRAWS_TEMPLATE} template"
            )
            story_list_title = "Scores and Draws"
            if not self.dry_run:
                try:
                    add_story_list_item(
                        self.site,
                        page,
                        story_list_title,
                        zone,
                        SCORES_AND_DRAWS_TEMPLATE,
                        limit=10,
                    )
                except StoryList.DoesNotExist:
                    # TODO: For now just ignore if we can't find the Scores and Draws storylist
                    self.log(
                        f"WARNING: Not adding Scores and Draws story list zone item as story list not found: {story_list_title}"
                    )
            else:
                # For dry run, at least check if storylist can be found
                try:
                    story_list = find_story_list(self.site, story_list_title)
                except StoryList.DoesNotExist:
                    self.log(
                        f"WARNING: Story list not found: {story_list_title}"
                    )
                else:
                    self.log(
                        f"Found story list: [{story_list.id}] {story_list}"
                    )

    def update_index_page_main_side_zone_items(self, page, is_primary_page):
        zone = MAIN_SIDE_ZONE
        zone_items = page.zone_items.filter(
            zone=zone,
        ).order_by("order")

        self.log(f"Zone: {zone}")

        self.log(f"Removing all ({zone_items.count()}) zone items")
        if not self.dry_run:
            zone_items.delete()
        if not is_primary_page:
            return

        # Continue with non-primary pages

        self.log(
            "Creating mailing list",
            False,
        )

        if not self.dry_run:
            support_text = "Sign up to receive our Breaking News Alerts and Editor's Daily Headlines featuring the best local news and stories."
            add_mailing_list_item(
                self.site,
                page,
                zone,
                DEFAULT_MAILING_LIST_TEMPLATE,
                text=support_text,
            )

    def update_index_page_right_zone_items(self, page, is_primary_page):
        if not self.is_ags:
            return
        zone = RIGHT_ZONE
        zone_items = page.zone_items.filter(
            zone=zone,
        ).order_by("order")

        self.log(f"Zone: {zone}")

        self.log(f"Removing all ({zone_items.count()}) zone items")
        if not self.dry_run:
            zone_items.delete()

    def update_homepage_zone_items(self, page):
        self.update_homepage_newswell_zone_items(page)
        self.update_homepage_newswell_side_zone_items(page)
        self.update_homepage_main_zone_items(page)
        self.update_homepage_main_side_zone_items(page)

    def update_index_page_zone_items(self, page, is_primary_page):
        self.update_index_page_main_zone_items(page, is_primary_page)
        self.update_index_page_main_side_zone_items(page, is_primary_page)
        self.update_index_page_right_zone_items(page, is_primary_page)

    def update_index_pages(self):
        self.log("Index pages:")

        # NOTE: We exclude pages created or shared pages generated from our previous create_pages() call.

        page_sites_subquery = Subquery(
            SitePage.objects.filter(page=OuterRef("id"))
            .values("page")
            .annotate(count=Count("id"))
            .values("count")
        )

        accessible_pages = (
            self.pages.annotate(count_sites=page_sites_subquery)
            .filter(accessible=True)
            .exclude(
                Q(url="/")
                | Q(id__in=self.created_page_ids)
                | Q(count_sites__gt=1)
            )
            .order_by("-sitepage__parent", "sitepage__order")
        )

        ags_visible_urls = (
            set(AGS_MENU_VISIBLE_URLS)
            | set(AGS_PRIMARY_NAV_URLS)
            | set(AGS_URLS_NON_MENU_KEEP)
        )

        for page in accessible_pages:
            self.log(f"Page: [{page.id}] {page.url} {page}")

            site_page = page.site_page_for_site(self.site)
            site_page_parent = site_page.parent
            is_primary_page = site_page_parent is None

            is_parent_page = accessible_pages.filter(
                sitepage__parent=site_page
            ).exists()

            if self.is_ags:
                # NOTE: We exclude redirect pages as making a redirected page inaccessible
                # can't be listed in the manage pages properly.
                if (
                    page.url
                    and not page.redirect_to
                    and page not in ags_visible_urls
                ):
                    # For Ags make these pages inaccessible
                    attrib_updates = {
                        "accessible": False,
                    }
                else:
                    urls_for_secondary_handing = (
                        [] if is_parent_page else [page.url]
                    )

                    # Note: update zone items first as they may use page.story_list

                    self.update_index_page_zone_items(
                        page,
                        is_primary_page
                        and page.url not in urls_for_secondary_handing,
                    )

                    attrib_updates = {
                        "story_list": None,
                    }

                    if page.url in AGS_PRIMARY_NAV_URLS:
                        attrib_updates["menu_visible"] = True
                        attrib_updates["menu_visible_primary"] = True
                        attrib_updates["template"] = (
                            SECONDARY_INDEX_PAGE_TEMPLATE
                        )
                    elif page.url in AGS_PRIMARY_NAV_URLS:
                        attrib_updates["menu_visible"] = True
                        attrib_updates["menu_visible_primary"] = False
                        attrib_updates["template"] = (
                            SECONDARY_INDEX_PAGE_TEMPLATE
                        )
                    elif page.url in ["contact", "contact-us"]:
                        attrib_updates["template"] = (
                            "content-two-top-cols.html"
                        )

                    # Need to make all pages first level (ie. delete parent) and also update url
                    # to be first level too. Will have to rename parent url so no conflict.

                    if not is_primary_page:
                        site_page.parent = None
                        if not self.dry_run:
                            site_page.save()

                    if "/" in page.url:
                        new_page_url = page.url.split("/")[-1]
                        attrib_updates["url"] = new_page_url

                        # Rename any existing page with that url first
                        try:
                            existing_page = self.pages.get(url=new_page_url)
                        except Page.DoesNotExist:
                            pass
                        else:
                            new_url = f"{existing_page.url}-AUTUMNIZED"
                            self.log(
                                f"updating dupe page {existing_page} to url {new_url}",
                                False,
                            )
                            existing_page.url = new_url
                            if not self.dry_run:
                                existing_page.save()
            else:
                urls_for_secondary_handing = ["video"]

                # Note: update zone items first as they may use page.story_list

                self.update_index_page_zone_items(
                    page,
                    is_primary_page
                    and page.url not in urls_for_secondary_handing,
                )

                attrib_updates = {
                    "story_list": None,
                }

                if is_primary_page:
                    is_priv_nav = page.name in PRIMARY_NAV_PAGES

                    attrib_updates["menu_visible_primary"] = is_priv_nav

                    if is_priv_nav:
                        attrib_updates["menu_visible"] = True
                    else:
                        self.log(f"menu_visible: {page.menu_visible}", True)

                if page.url in ["advertise", "advertising"]:
                    attrib_updates["url"] = "advertise"
                    attrib_updates["name"] = "Advertise"
                elif page.url == "subscribe":
                    attrib_updates["name"] = "Full Digital Access"

                template = self.page_template_conversion(
                    page,
                    is_primary_page=is_primary_page
                    and page.url not in urls_for_secondary_handing,
                )
                attrib_updates["template"] = template

            self.update_instance_attribs(page, attrib_updates)

    def update_nav_shortcuts(self, navigation, is_strap=False):
        is_dict = isinstance(navigation, dict)
        have_updates = False
        for idx, shortcut_item in enumerate(NAV_SHORTCUTS):
            shortcuts = (
                shortcut_item
                if isinstance(shortcut_item, list)
                else [shortcut_item]
            )

            for shortcut in shortcuts:
                if shortcut.get("url_from_rev_map"):
                    try:
                        shortcut["url"] = DOMAIN_TO_REV[self.domain]
                    except KeyError:
                        self.log(
                            f"WARNING: No REV url found for {self.domain}"
                        )
                        continue

                if (
                    not shortcut["url"].startswith("http")
                    and not self.pages.filter(
                        url=shortcut["url"].strip("/")
                    ).exists()
                ):
                    # TODO: Create page if not found (podcast in particular at least)
                    self.log(
                        f"INFO: No page found for shortcut url {shortcut['url']}"
                    )
                    continue

                for field in NAV_SHORTCUT_FIELDS:
                    if not is_strap and field == "cta":
                        continue
                    attrib_name = f"shortcut_{idx + 1}_{field}"
                    value = shortcut[field]
                    is_same = (
                        not is_dict
                        and getattr(navigation, attrib_name) == value
                    )
                    if not is_same:
                        if is_dict:
                            navigation[attrib_name] = value
                        else:
                            setattr(navigation, attrib_name, value)
                        have_updates = True
                    self.log(f"{attrib_name}: {value}", is_same)
                break
            else:
                self.log(
                    f"WARNING: Skipping shortcut_{idx + 1} as no page matching url(s) {shortcuts}"
                )

            template = "shortcuts_strap.html" if is_strap else "nav2.html"
            is_same = not is_dict and navigation.template == template
            if not is_same:
                if is_dict:
                    navigation["template"] = template
                else:
                    navigation.template = template
                have_updates = True
            self.log(f"template: {template}", is_same)

        return have_updates

    def update_nav_external_inks(self, navigation, use_dict=False):
        have_updates = False
        for idx, link_item in enumerate(NAV_EXTERNAL_LINKS):
            external_links = (
                link_item if isinstance(link_item, list) else [link_item]
            )

            for external_link in external_links:
                for field in NAV_EXTERNAL_LINK_FIELDS:
                    attrib_name = f"external_link_{idx + 1}_{field}"
                    value = external_link[field]
                    if field == "url":
                        value = value.format(
                            domain=re.sub(r"^beta", "www", self.site.domain)
                        )
                    is_same = (
                        not use_dict
                        and getattr(navigation, attrib_name) == value
                    )
                    if not is_same:
                        if use_dict:
                            navigation[attrib_name] = value
                        else:
                            setattr(navigation, attrib_name, value)
                        have_updates = True
                    self.log(f"{attrib_name}: {value}", is_same)

        return have_updates

    def update_global_nav_zone_item(self):
        self.log("Global navigation shortcuts:")
        try:
            nav_zone_item = self.zone_items.get(
                element_type="navigation",
                page=None,
                zone=NAVIGATION_ZONE,
            )
        except ZoneItem.DoesNotExist:
            self.log("WARNING: Navigation global zone item not found")
            return

        have_updates = self.update_nav_shortcuts(nav_zone_item.navigation)
        have_updates |= self.update_nav_external_inks(nav_zone_item.navigation)
        if have_updates and not self.dry_run:
            nav_zone_item.navigation.save()

    def update_other_global_zones(self):
        if self.is_ags:
            self.log(
                f"Deleting all zone items in zones {AGS_ZONES_DELETE_ALL}"
                f" and non-nav zoneitem in {NAVIGATION_ZONE}",
                False,
            )
            zone_items = self.zone_items.filter(
                Q(zone__in=AGS_ZONES_DELETE_ALL)
                | Q(~Q(element_type="navigation"), zone=NAVIGATION_ZONE),
                page=None,
            )
            if not self.dry_run:
                zone_items.delete()

    def update_global_footer_zone_item(self):
        self.log("Global footer:")

        code_snippet = None
        try:
            old_footer_zone_item = self.zone_items.get(
                element_type="codesnippet",
                page=None,
                zone=FOOTER_ZONE,
            )
            code_snippet = old_footer_zone_item.codesnippet
        except ZoneItem.DoesNotExist:
            if not self.is_ags:
                self.log("WARNING: Navigation global zone item not found")
        except ZoneItem.MultipleObjectsReturned:
            if not self.is_ags:
                self.log(
                    "WARNING: Multiple global footer codesnippets detected"
                )
        except CodeSnippet.DoesNotExist:
            if not self.is_ags:
                self.log(
                    f"WARNING: Old nav codesnippet instance doesn't exist for zone item {old_footer_zone_item}"
                )

        # Gut any previously existing new global header zone items
        footer_zones = [f"footer_nav_{i}" for i in range(1, 5)]
        self.log(
            f"Deleting all zone items in new footer zones: {footer_zones}",
            False,
        )
        if not self.dry_run:
            self.zone_items.filter(page=None, zone__in=footer_zones).delete()

        new_zone_items = (
            [
                [
                    {
                        "element_type": "heading",
                        "heading": "Australian Community Media",
                    },
                    {
                        "element_type": "menulist",
                        "page_name": "Privacy",
                        "page_url": "privacy",
                    },
                    {
                        "element_type": "menulist",
                        "page_name": "Conditions of Use",
                    },
                    {
                        "element_type": "menulist",
                        "page_name": "Terms and Conditions - Digital Subscription",
                        "page_url": "about-us/terms-conditions/digital-subscription",
                    },
                    {
                        "element_type": "menulist",
                        "page_name": "Terms and Conditions - Newspaper Subscription",
                        "page_url": "about-us/terms-conditions/newspaper-subscription",
                    },
                ],
                [
                    {"element_type": "heading", "heading": self.publication},
                    {
                        "element_type": "menulist",
                        "page_name": "Contact Us",
                        "page_url": "contact-us",
                    },
                    {
                        "element_type": "menulist",
                        "page_name": "Today's Paper",
                        "page_url": "digital-print-edition",
                    },
                    {
                        "element_type": "heading",
                        "heading": "Help Centre",
                        "url": "https://australiancommunitymedia.zendesk.com/hc/en-us",
                        "open_new_window": True,
                    },
                ],
                [
                    {
                        "element_type": "heading",
                        "heading": "Farmonline Network",
                    },
                    {
                        "element_type": "heading",
                        "heading": "AgTrader",
                        "open_new_window": True,
                        "url": "https://www.agtrader.com.au/",
                    },
                    {
                        "element_type": "heading",
                        "heading": "Rural Events",
                        "open_new_window": True,
                        "url": "https://acmruralevents.com.au/",
                    },
                    {
                        "element_type": "heading",
                        "heading": "Rural Book Shop",
                        "open_new_window": True,
                        "url": "https://www.ruralbookshop.com.au/",
                    },
                    {
                        "element_type": "heading",
                        "heading": "Live Stock Connect",
                        "open_new_window": True,
                        "url": "https://livestockconnect.com.au/",
                    },
                    {
                        "element_type": "heading",
                        "heading": "Horse Deals",
                        "open_new_window": True,
                        "url": "https://www.horsedeals.com.au/",
                    },
                ],
            ]
            if self.is_ags
            else [
                [
                    {
                        "element_type": "heading",
                        "heading": "Australian Community Media",
                    },
                    {
                        "element_type": "menulist",
                        "page_name": "Privacy",
                        "page_url": "privacy",
                    },
                    {
                        "element_type": "menulist",
                        "page_name": "Conditions of Use",
                    },
                    {
                        "element_type": "menulist",
                        "page_name": "Terms and Conditions - Digital Subscription",
                        "page_url": "about-us/terms-conditions/digital-subscription",
                    },
                    {
                        "element_type": "menulist",
                        "page_name": "Terms and Conditions - Newspaper Subscription",
                        "page_url": "about-us/terms-conditions/newspaper-subscription",
                    },
                ],
                [
                    {"element_type": "heading", "heading": self.publication},
                    {
                        "element_type": "menulist",
                        "page_name": "Contact",
                        "page_url": "contact",
                    },
                    {
                        "element_type": "menulist",
                        "page_name": "About Us",
                        "page_url": "about-us",
                    },
                    {
                        "element_type": "menulist",
                        "page_name": "Advertise",
                        "page_url": "advertise",
                    },
                    {
                        "element_type": "menulist",
                        "page_name": "Today's Paper",
                        "page_url": "digital-print-edition",
                    },
                    {
                        "element_type": "heading",
                        "heading": "Property Digital Edition",
                        "lookup_url": True,
                        "optional": True,
                        "open_new_window": True,
                    },
                    {
                        "element_type": "heading",
                        "heading": "Help Centre",
                        "url": "https://australiancommunitymedia.zendesk.com/hc/en-us",
                        "open_new_window": True,
                    },
                    {
                        "element_type": "menulist",
                        "menu_name": "Newsletters",
                        "lookup_url": True,
                        "optional": True,
                    },
                ],
                [
                    {"element_type": "heading", "heading": "Our Sites"},
                    {
                        "element_type": "heading",
                        "heading": "Real Estate View",
                        "url_from_rev_map": True,
                        "open_new_window": True,
                    },
                    {
                        "element_type": "heading",
                        "heading": "Explore",
                        "url": "https://www.exploretravel.com.au/",
                        "open_new_window": True,
                    },
                    {
                        "element_type": "heading",
                        "heading": "Beevo",
                        "url": "https://www.beevo.com.au/",
                        "open_new_window": True,
                    },
                    {
                        "element_type": "menulist",
                        "page_name": "Business",
                    },
                    {
                        "element_type": "heading",
                        "heading": "Place an Ad",
                        "url": "https://addirect.com.au/",
                        "open_new_window": True,
                    },
                    {
                        "element_type": "menulist",
                        "page_name": "Classifieds",
                    },
                    {
                        "element_type": "heading",
                        "heading": "Cars",
                        "url": "https://www.countrycars.com.au/",
                        "open_new_window": True,
                    },
                    {
                        "element_type": "menulist",
                        "page_name": "Jobs",
                    },
                    {
                        "element_type": "heading",
                        "heading": "Tributes",
                        "lookup_url": True,
                        "open_new_window": True,
                    },
                    {
                        "element_type": "heading",
                        "heading": "Celebrations",
                        "lookup_url": True,
                        "open_new_window": True,
                    },
                    {
                        "element_type": "heading",
                        "heading": "Promo Codes",
                        "url": "https://www.australiancoupons.com.au/",
                        "open_new_window": True,
                    },
                    {
                        "element_type": "heading",
                        "heading": "AgTrader",
                        "lookup_url": True,
                        "open_new_window": True,
                    },
                    {
                        "element_type": "heading",
                        "heading": "Whizz",
                        "url": "https://whizz.com.au/",
                        "open_new_window": True,
                    },
                    {
                        "element_type": "heading",
                        "heading": "Garage Sales",
                        "lookup_url": True,
                        "open_new_window": True,
                    },
                ],
                [
                    {"element_type": "heading", "heading": "Submit"},
                    {
                        "element_type": "menulist",
                        "page_name": "Send a Letter to the Editor",
                        "page_url": "comment/letters-to-the-editor",
                    },
                    {
                        "element_type": "menulist",
                        "page_name": "Send us your news",
                        "page_url": "news/send-us-your-news",
                    },
                ],
            ]
        )

        self.log("Adding footer zone items")

        if code_snippet:
            # NOTE: This should only be found if a lego site or autumn when re-run after being a lego site.
            soup = BeautifulSoup(code_snippet.code, features="html5lib")
            anchors = soup.find_all("a")

        template = FOOTER_NAV_TEMPLATE

        for idx, zone_items in enumerate(new_zone_items):
            zone = f"footer_nav_{idx + 1}"

            for item in zone_items:
                element_type = item["element_type"]

                if element_type == "menulist":
                    # Try page_url first (if present), then fall back to page name
                    menu_list_page = None

                    optional = item.get("optional", False)
                    if item.get("lookup_url"):
                        if not code_snippet:
                            self.log(
                                f"WARNING: Need lego footer codesnippet to lookup url for {item}"
                            )
                            continue

                        url_from_code_snippet = (
                            self.find_url_in_code_snippet_anchors(
                                anchors, item["menu_name"], optional
                            )
                        )
                        if not url_from_code_snippet:
                            continue

                        # Extract page path from code s
                        parse_result = urlparse(url_from_code_snippet)
                        page_url = parse_result.path.strip("/")
                    else:
                        page_url = item.get("page_url")

                    if page_url:
                        try:
                            menu_list_page = self.pages.get(
                                accessible=True, url=page_url
                            )
                        except Page.DoesNotExist:
                            self.log(
                                f"WARNING: Could not find page matching url {page_url} for footer menulist"
                            )
                        else:
                            # NOTE: There were cases of footer urls mapping to site pages that were redirects to final site page.
                            # Follow any page redirections (assume to local page)
                            if menu_list_page.redirect_to:
                                try:
                                    menu_list_page = self.pages.get(
                                        accessible=True,
                                        url=menu_list_page.redirect_to,
                                    )
                                except Page.DoesNotExist:
                                    menu_list_page = None
                                    self.log(
                                        f"WARNING: Could not find page matching url {menu_list_page.redirect_to} redirected from {page_url} for footer menulist"
                                    )

                    if not menu_list_page:
                        page_name = item.get("page_name")
                        if page_name:
                            try:
                                menu_list_page = self.pages.get(
                                    accessible=True, name=page_name
                                )
                            except Page.DoesNotExist:
                                self.log(
                                    f'WARNING: Could not find page matching name "{page_name}" for footer menulist'
                                )

                    if not menu_list_page:
                        self.log(
                            "WARNING: Unable to add footer menulist as no page matching url nor name"
                        )
                        continue

                    # Add menulist
                    self.log(
                        f'{zone} menulist: "{menu_list_page}" {menu_list_page.url}',
                        False,
                    )
                    if not self.dry_run:
                        add_menu_list_item(
                            self.site,
                            None,
                            zone,
                            template,
                            menu_list_page=menu_list_page,
                        )

                elif element_type == "heading":
                    optional = item.get("optional", False)
                    if item.get("lookup_url"):
                        if not code_snippet:
                            self.log(
                                f"WARNING: Need lego footer codesnippet to lookup url for {item}"
                            )
                            continue
                        url = self.find_url_in_code_snippet_anchors(
                            anchors, item["heading"], optional
                        )
                        if not url:
                            continue
                    elif item.get("url_from_rev_map"):
                        try:
                            url = DOMAIN_TO_REV[self.domain]
                        except KeyError:
                            self.log(
                                f"WARNING: No REV url found for {self.domain}"
                            )
                            continue
                    else:
                        url = item.get("url", "")
                    # Add heading
                    self.log(
                        f'{zone} heading: "{item["heading"]}" {url}', False
                    )
                    if not self.dry_run:
                        add_heading_item(
                            self.site,
                            None,
                            zone,
                            item["heading"],
                            template=template,
                            url=url,
                            open_new_window=item.get("open_new_window", False),
                        )

        self.log(f"Deleting all zone items in old footer zone: {zone}", False)
        if not self.dry_run:
            self.zone_items.filter(page=None, zone=FOOTER_ZONE).delete()

    def find_url_in_code_snippet_anchors(self, anchors, text, optional=False):
        for anchor in anchors:
            if anchor.text.strip() == text:
                href = anchor["href"]
                if href:
                    url = href.strip()
                    self.log(
                        f'Extracted URL of "{text}" anchor from footer code snippet: {url}'
                    )
                    return url
                if not optional:
                    self.log(
                        f'WARNING: Anchor for "{text}" in footer code snippet didn\'t have HREF.'
                    )

        else:
            prefix = "INFO" if optional else "WARNING"
            self.log(
                f'{prefix}: Could not find "{text}" in footer code snippet.'
            )

    def create_pages(self):
        dpe_url = "digital-print-edition"
        puzzles_url = "life-style/puzzles"
        community_url = "community"
        newsletters_url = "newsletters"

        # Today's Paper (DPE)

        try:
            old_dpe_page = self.pages.get(url=dpe_url)
        except Page.DoesNotExist:
            old_dpe_page = None
        else:
            if not old_dpe_page.accessible:
                old_dpe_page.delete()
                old_dpe_page = None

        if not old_dpe_page:
            self.log("Creating Today's Paper page", False)
            meta_title = f"Today's Paper | {self.publication}"
            meta_description = f"Access the online version of newspaper from {self.publication}"

            dpe_page_kwargs = dict(
                name="Today's Paper",
                enable_zendesk=True,
                menu_visible=True,
                meta_description=meta_description,
                meta_title=meta_title,
                site=self.site,
                template="content.html",
                url=dpe_url,
            )
            if not self.dry_run:
                dpe_page = Page.objects.create(**dpe_page_kwargs)
                self.created_page_ids.append(dpe_page.id)
                SitePage.objects.create(
                    site=self.site,
                    page=dpe_page,
                )
                # Add dpelist zone item
                add_dpelist_item(
                    self.site,
                    dpe_page,
                    MAIN_ZONE,
                    limit=10,
                    template="index.html",
                )

        # Newsletter

        if self.is_lego:
            # Only add newsletters page when puzzles page not present.
            if (
                not self.pages.filter(url=puzzles_url).exists()
                and not self.pages.filter(url=newsletters_url).exists()
            ):
                self.log("Creating Newsletters page", False)
                meta_title = f"Newsletters | {self.publication}"
                meta_description = f"Catch up on the latest news with {self.publication} newsletters."

                newsletter_page_kwargs = dict(
                    name="Newsletters",
                    meta_description=meta_description,
                    meta_title=meta_title,
                    site=self.site,
                    template=PRIMARY_INDEX_PAGE_TEMPLATE,
                    url=newsletters_url,
                )

                try:
                    community_site_page = SitePage.objects.get(
                        site=self.site, page__url=community_url
                    )
                except SitePage.DoesNotExist:
                    community_site_page = None
                    self.log(
                        "Parent Community page, not found, Newsletter page will be at root level",
                        False,
                    )
                else:
                    self.log("Found Community page for parent", False)

                if not self.dry_run:
                    newsletters_page = Page.objects.create(
                        **newsletter_page_kwargs
                    )
                    self.created_page_ids.append(newsletters_page.id)
                    SitePage.objects.create(
                        site=self.site,
                        page=newsletters_page,
                        parent=community_site_page,
                    )

        # Header and Footer pages

        if self.is_lego:
            for url, values in NEW_PAGES.items():
                try:
                    page = self.pages.get(url=url)
                except Page.DoesNotExist:
                    self.log(f"creating {url} page", False)
                    if not self.dry_run:
                        page = Page.objects.create(**values, url=url)
                        self.created_page_ids.append(page.id)
                        SitePage.objects.create(
                            site=self.site,
                            page=page,
                        )
                else:
                    self.log(f"{url} page already exists", True)

        # Shared Pages (shared from an existing source site)
        if self.is_lego and self.is_prod:
            try:
                source_domain = "www.newcastleherald.com.au"
                source_site = Site.objects.get(domain=source_domain)
            except Site.DoesNotExist:
                self.log(
                    f"WARNING: Skipping creating shared pages as source site not found: {source_domain}"
                )
            else:
                self.log(f"Finding shared pages on site: {source_domain}")
                shared_pages = source_site.page_set.all()
                for url in SHARED_PAGE_URLS:
                    try:
                        source_shared_page = shared_pages.get(url=url)
                    except Page.DoesNotExist:
                        self.log(f"WARNING: Shared page {url} not found")
                    else:
                        # Delete any page with same url
                        local_pages = self.pages.filter(url=url)
                        if local_pages:
                            self.log(
                                f"Deleting existing local pages with same url {url}",
                                False,
                            )
                            if not self.dry_run:
                                local_pages.delete()

                        source_site_page = (
                            source_shared_page.site_page_for_site(source_site)
                        )

                        # Note: This logic copied from suzuka.pages.forms.py #575

                        if not source_site_page.parent:
                            parent = None
                        else:
                            parent_name = source_site_page.parent.page.name
                            potential_parents = SitePage.objects.filter(
                                site=self.site, page__name=parent_name
                            )
                            potential_parents = potential_parents.filter(
                                parent=None
                            )
                            parent = potential_parents.first()

                        sitepage_kwargs = {
                            "page": source_shared_page,
                            "order": source_site_page.order,
                            "url": source_site_page.url,
                        }

                        self.log(
                            f"Sharing page {url} under parent page: {parent}",
                            False,
                        )
                        if not self.dry_run:
                            SitePage.objects.create(
                                site=self.site,
                                parent=parent,
                                **sitepage_kwargs,
                            )

    def update_global_zone_items(self):
        self.update_other_global_zones()
        self.update_global_nav_zone_item()
        self.update_global_footer_zone_item()

    def run(self):
        self.update_site()
        self.update_settings()
        self.create_pages()
        self.update_homepage()
        self.update_index_pages()
        self.update_global_zone_items()


class Command(BaseCommand):
    help = """Convert a site from Lego or Ags theme to Autumn theme.
        (WARNING: Don't re-run on same site after successful conversion)"""

    def add_arguments(self, parser):
        """Configure the command line arguments."""
        parser.add_argument("domain")
        parser.add_argument(
            "-V",
            "--verbose",
            action="store_true",
            default=False,
            help="Verbose mode: will show logs of items not updated also",
        )
        parser.add_argument(
            "-d",
            "--dryrun",
            action="store_true",
            default=False,
            help="Dry Run",
        )

    @transaction.atomic
    def handle(self, *args, **options):
        """Update the target site."""
        domain = options["domain"]
        verbose = options["verbose"]
        dry_run = options["dryrun"]

        try:
            site = Site.objects.select_related("settings").get(domain=domain)
        except Site.DoesNotExist:
            raise CommandError("Target site does not exist")

        self.stdout.write(f"** {'DRY' if dry_run else 'LIVE'} RUN **")

        transformer = SiteTransformer(
            self, site, dry_run=dry_run, verbose=verbose
        )
        transformer.run()
