# Generated by Django 3.1.14 on 2025-11-12 08:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('conf', '0208_features_supportersitefeature_enabled'),
    ]

    operations = [
        migrations.AddField(
            model_name='settings',
            name='declustered',
            field=models.BooleanField(default=False, help_text='Indicates whether the site is configured to support single-site only access'),
        ),
    ]
