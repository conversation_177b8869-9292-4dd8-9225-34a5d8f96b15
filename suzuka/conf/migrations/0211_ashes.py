# Generated by Django 3.1.14 on 2025-11-24 06:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('conf', '0210_the_ashes'),
    ]

    operations = [
        migrations.AlterField(
            model_name='settings',
            name='sportshubsponsorfeature_featured_sport_sponsor',
            field=models.CharField(blank=True, choices=[('afl', 'AFL'), ('cricket', 'Cricket'), ('nrl', 'NRL'), ('a-league', 'A-League'), ('t20', 'T20'), ('basketball', 'Basketball'), ('hockey', 'Hockey'), ('netball', 'Netball'), ('oneDay', 'One Day Cricket')], help_text='\n        If selected sport has a sponsor data, it will be used on \n        general sport index pages e.g. /sport or /sport/local-sport\n        ', max_length=20, verbose_name='Featured Sport Sponsor'),
        ),
    ]
