import json
import logging
import time
from random import shuffle
from typing import Any, Optional, cast, no_type_check
from urllib.parse import urljoin
from wsgiref.util import is_hop_by_hop

import requests
from django.conf import settings
from django.forms.models import model_to_dict
from django.http import HttpRequest, HttpResponse
from django.middleware.csrf import get_token
from django.utils.safestring import mark_safe
from django.utils.text import slugify
from pitcrews_oauth2.authentication import OAuth2Authentication
from silverstone_client.client import Story
from slumber.exceptions import SlumberBaseException

from suzuka.ab_testing.middleware import HasExperiment
from suzuka.auctions.utils import (
    serialise_auction_for_view,
    serialize_event_calendar_for_view,
)
from suzuka.classifieds.settings import CLASSIFIED_LIST_MAX_CLASSIFIEDS
from suzuka.clearing_sales.utils import serialise_clearing_sale_for_view
from suzuka.comments.models import FeaturedComment
from suzuka.conf.context_processors import conf
from suzuka.conf.models import Settings
from suzuka.conf.sites import current_site, current_site_id
from suzuka.conf.utils import (
    DEFAULT_GALLERY_LAYOUT,
    DEFAULT_GALLERY_STYLE,
    GALLERY_LAYOUT_CHOICES,
    GALLERY_STYLES_CHOICES,
    get_org_data,
    has_chaperone_site,
)
from suzuka.dailymotion.utils import DailymotionVideoStory
from suzuka.elements_demo.models import (
    StoryList as StoryListEl,
)
from suzuka.emags.utils import EMagsDynamoDB
from suzuka.mailinglists.marketing_cloud import (
    get_remote_national_mail_groups,
    get_remote_regional_mail_groups,
)
from suzuka.mailinglists.providers import PROVIDERS
from suzuka.outages.utils import get_filtered_outages
from suzuka.pages.editing.utils import has_edit_perms
from suzuka.pages.logos import logo_svg_urls
from suzuka.pages.models import Page, SitePage, ZoneItem
from suzuka.pages.monaco import get_react_host
from suzuka.pages.recirculation import (
    get_all_recirculation_sections,
)
from suzuka.pages.templatetags.menu_tags import logo_url
from suzuka.pages.templatetags.page_tags import (
    dpe_links,
    dpe_published_dates,
    site_optional_pages,
    subscription_prices,
)
from suzuka.pages.utils import (
    SCORES_AND_DRAWS_TEMPLATE,
    clean_story_element,
    create_redirect_response,
    format_collection_page,
    get_cached_zone_items,
    get_dpe_index_page_url,
    get_edit_mode,
    get_page_hierarchy,
    get_parent_from_page_hierarchy,
    get_story_author_piano_uids,
    has_multiauth,
    is_premium_request,
    is_show_beta,
    is_story_blacklisted_for_skimlinks,
    is_zone_item_published,
    page_for_story,
    pull_sub_pages,
)
from suzuka.stories.google_extended_access import (
    is_gnews_extended_access_article,
)
from suzuka.stories.models import StoryList
from suzuka.stories.settings import STORY_LIST_MAX_STORIES
from suzuka.stories.templatetags.story_tags import (
    dpe_host,
    get_businessfeature_labels,
    get_businessfeature_titles,
    get_cartoon_storylist_signpost,
    get_first_stories_for,
    get_first_story_list,
    is_always_external_link,
    is_businessfeature_instance,
    is_cartoon_instance,
    select_lead_image,
    should_noindex_story,
    story_url_for,
    transform_host,
)
from suzuka.stories.utils import (
    PAYWALL_CT_FREE_TAG,
    PAYWALL_FREE_TAG,
    StoryEncoder,
    add_free_signpost_tag,
    get_canonical_site_details,
    get_cleaned_pinned_stories,
    get_story,
    get_topic_stories,
    has_lead_image,
    serialise_story_for_view,
)
from suzuka.subscriptions.models import (
    EnterpriseSubscriptionSite,
    PaywallCampaign,
)
from suzuka.subscriptions.utils import cluster_summary_for_site
from suzuka.ugc.settings import UGC_LIST_MAX_ITEMS

logger = logging.getLogger(__name__)

NEXT_JS_HMR_COOKIE = "__next_hmr_refresh_hash__"


def add_story_attributes(
    context,
    stories,
    storylist,
    stories_for_bf=False,
    use_canonical_url=False,
    show_canonical_site=False,
):
    """
    Add Story Attributes.
    Takes story model objects list and their corresponding jsonified objs and adds extra data attrs
    (e.g. story urls).
    """
    is_external_story = False
    if isinstance(storylist, StoryListEl):
        is_external_story = storylist.list_type != StoryListEl.TYPE_CHOICES.NA
    # Convert story instances to more manageable dicts.
    story_dicts = []
    for story in stories:
        story_dict = json.loads(json.dumps(story, cls=StoryEncoder))
        if show_canonical_site:
            if canonical_site_details := get_canonical_site_details(
                story, context.get("STATIC_URL")
            ):
                story_dict |= canonical_site_details
            else:
                continue
        story_dict["id"] = story.id
        story_dict["external_link"] = False
        story_dict["canonical_url"] = story.canonical_url

        if not context.get("conf", {}).get(
            "disable_signpost_for_story_free_tag"
        ):
            story_dict["tags"] = add_free_signpost_tag(story_dict["tags"])

        story_dict["show_lead_image"] = getattr(
            story, "show_lead_image", True
        ) in (True, None)

        story_dict["show_summary"] = bool(
            getattr(story, "show_summary", False)
        )

        if stories_for_bf:
            story_dict["story_url"] = story_url_for(
                {
                    "_story_list": story.story_list,
                },
                story,
            )
            story_dict["title"] = story.story_list.title
        else:
            context.update(
                {
                    "_story_list": storylist.story_list,
                    "is_external_story": is_external_story,
                    "_story_list_element": storylist,
                }
            )
            story_dict["story_url"] = story_url_for(
                context, story, storylist=storylist.story_list
            )
            # allhomes stories are always an external link
            story_dict["external_link"] = is_always_external_link(story)

        if use_canonical_url:
            story_dict["canonical_url"] = story_url_for(
                {
                    "_story_list_element": storylist,
                    "is_external_story": is_external_story,
                },
                story,
            )
            story_dict["external_link"] = True

        story_dict["lead_image"] = select_lead_image(context, story)
        story_dict["lead_brightcove_id"] = getattr(
            story, "lead_brightcove_id", None
        )
        story_dict["snapshot"] = getattr(story, "snapshot", None)
        story_dict["lead_brightcove_id"] = getattr(
            story, "lead_brightcove_id", None
        )
        story_dict["element_types"] = getattr(story, "element_types", [])
        story_dicts.append(story_dict)
    return story_dicts


def is_editable(request):
    return has_multiauth(request, [OAuth2Authentication(scopes=["suzuka"])])


def prerender_layout(request, context):
    """Pre-render Layout.

    Render react component - send json and template name so react server can render to flat html.
    """

    page = context.get("page")
    story_template = context.get("story_template")
    react_context = context["react_context"]

    base_url = get_react_host()
    api_url = settings.REACT_API_ENDPOINT

    if story_template:
        component_name = story_template
    elif page:
        component_name = page.template
    else:
        component_name = context.get("template_name", "")

    react_server_url = urljoin(base_url, api_url, slugify(component_name))

    if cookie := request.COOKIES.get(NEXT_JS_HMR_COOKIE):
        cookies = {NEXT_JS_HMR_COOKIE: cookie}
    else:
        cookies = None

    # Pass Next.js HMR related headers
    headers = {
        k: v
        for k, v in request.headers.items()
        if "rsc" == (k_lower := k.lower())
        or k_lower.startswith("next-")
        or k_lower.startswith("x-")
    }

    try:
        req_response = requests.post(
            react_server_url,
            json=react_context,
            params=request.GET,
            headers=headers,
            cookies=cookies,
            allow_redirects=False,
        )
        if req_response.is_redirect:
            return {
                "redirect": {
                    "location": req_response.headers["Location"],
                    "status_code": req_response.status_code,
                },
            }
        if not settings.DEBUG:
            req_response.raise_for_status()
        if context.get("full_page_render"):
            return {"html": req_response.text, "headers": req_response.headers}
        json_resp = req_response.json()
    except BaseException as err:
        logger.exception(err)
        # Note: Our CloudFront CDN will not serve a 500 response and will serve a stale
        # cached version of the page for up to an hour (per config at time of writing)
        raise

    # Add titans non conditional ads to the context so that it can be
    # appended to the datalayer when rendering the page.
    context["initial_sitebuilder_state"] = json_resp.get("state", {})
    context["react_titan_ads"] = (
        json_resp.get("state", {}).get("ads", {}).get("nonConditionalAds", [])
    )

    # hard code the polar 6x1 ads for now
    context["react_titan_ads"].append("adspot-6x1-pos1")
    html_res = json_resp.get("html", "")

    return {"html": html_res}


def get_page_tree(request: HttpRequest) -> dict:
    menu_items = Page.current_site.menu_items(
        menu_visible=None
    ).select_related("story_list")
    if not has_edit_perms(request):
        menu_items = menu_items.filter(draft=False)

    return {
        page_id: [
            child.menu_nav_data
            for child in children
            if child.menu_visible or child.menu_visible_primary
        ]
        for (page_id, children) in menu_items.as_tree().items()
    }


def prepare_zone_item_data(request, context, **kwargs):
    """
    Get appropriate zone items/zone names for a page.
    """
    conf_settings = context.get("conf", {})
    features = conf_settings.get("features")
    is_editable = context.get("is_editable", False)
    is_editmode = get_edit_mode(request) == "editmode"
    is_story = context.get("story") is not None
    full_page_render = context.get("full_page_render")
    callbacks = kwargs.get("callbacks", {})

    order_by = ("zone", "order")

    filters_global_zone_items = {"site_id": current_site_id(), "page_id": None}

    filters = {}
    if full_page_render:
        zone_context_data = {
            "global": [],
            "page": [],
        }
        page = context.get("page")

        if page and page.id:
            filters = {"page_id": page.id}
    else:
        zone_context_data = []
        # For current homepage zones only rendering, for safety make sure
        # only consider those two zones as before.
        page = context["page"]
        if page and page.id:
            filters = {
                "page_id": page.id,
                "zone__in": ("newswell", "main"),
            }
        else:
            filters = {
                "zone__in": ("newswell", "main"),
            }

    # Load cached zone items for a page and
    # Load global zone items if page is full render
    # Don't load page zone items if story
    if filters and not is_story:
        zone_items = ZoneItem.objects.filter(**filters).order_by(*order_by)
        if not is_editmode:
            zone_items = get_cached_zone_items(zone_items, filters)
    else:
        zone_items = ()

    if full_page_render:
        all_global_zone_items = ZoneItem.objects.filter(
            **filters_global_zone_items
        ).order_by(*order_by)
        if not is_editmode:
            all_global_zone_items = get_cached_zone_items(
                all_global_zone_items, filters_global_zone_items
            )
            zone_items = all_global_zone_items + zone_items
        else:
            zone_items = tuple(all_global_zone_items) + tuple(zone_items)

    page_tree = None

    for zone_item in zone_items:
        data = {}
        zone_item_render = True
        pop_last_zone_item = False
        zone_element = zone_item.element()
        zone = zone_item.zone
        if not zone_element:
            continue
        useClientFetch = is_editable and hasattr(zone_element, "stories")
        if hasattr(zone_element, "stories"):
            # Business feature templates require a specialized storylist
            is_bf_stories = zone_element.template.startswith("bf-")
            zone_elements_with_stories = ["StoryList", "ClusteredStoryList"]
            is_sports_results_stories = (
                zone_element.template == SCORES_AND_DRAWS_TEMPLATE
            )
            get_stories = callbacks.get("get_stories")
            if is_bf_stories:
                useClientFetch = False
                stories = get_first_stories_for(
                    context,
                    "story-businessfeatures",
                    use_cache=not is_editmode,
                )
                # What is returned isnt a real storylist but rather an aggregated list of first
                # stories from a number of storylists. We wont support pinning since this
                # is not a normal storylist.
                data["pinned_story_ids"] = []
            elif (
                get_stories
                and zone_item.page
                and zone == "main"
                and callable(get_stories)
            ):
                stories = get_stories(element=zone_element)
            else:
                extra_kwargs = {}
                if isinstance(zone_element, StoryListEl):
                    extra_kwargs["request"] = request
                stories = (
                    zone_element.stories(
                        use_cache=not is_editmode, **extra_kwargs
                    )
                    if not useClientFetch
                    else []
                )
                data["pinned_story_ids"] = (
                    get_cleaned_pinned_stories(zone_element.story_list)
                    if not useClientFetch
                    else []
                )

                if zone_element.template == "echidna.html":
                    # Get the full story so the author mugshot details are
                    # available
                    stories = [get_story(story.id) for story in stories]
            data["total_stories"] = len(stories)
            show_canonical_site = getattr(
                zone_element, "show_canonical_site", False
            )

            if (
                full_page_render
                and type(zone_element).__name__ in zone_elements_with_stories
            ):
                limit = zone_element.limit or STORY_LIST_MAX_STORIES
                if zone_element.offset is None or zone_element.offset < 1:
                    offset = 0
                else:
                    offset = zone_element.offset - 1
                stories = stories[offset : offset + limit]
            zone_stories = add_story_attributes(
                context,
                stories,
                zone_element,
                stories_for_bf=is_bf_stories,
                use_canonical_url=zone_element.use_canonical_url,
                show_canonical_site=show_canonical_site,
            )

            data["template"] = zone_element.template
            data["story_list_id"] = zone_element.story_list_id
            data["story_list_title"] = getattr(
                zone_element.story_list, "title", ""
            )
            data["use_canonical_url"] = zone_element.use_canonical_url
            data["show_canonical_site"] = show_canonical_site
            if hasattr(zone_element, "summary_options"):
                data["summary_options"] = zone_element.summary_options
            if hasattr(zone_element, "allow_ads"):
                data["allow_ads"] = zone_element.allow_ads
            if hasattr(zone_element, "disable_bottom_border"):
                data["disable_bottom_border"] = (
                    zone_element.disable_bottom_border
                )
            if hasattr(zone_element, "pinned_stories_only"):
                # `pinned_stories_only` can be set in ZoneItem object or
                # in StoryList object (from models.py/stories)
                story_list = getattr(zone_element, "story_list", None)
                story_list_pinned_stories_only = (
                    story_list.pinned_stories_only if story_list else False
                )
                data["pinned_stories_only"] = (
                    zone_element.pinned_stories_only
                    or story_list_pinned_stories_only
                )
            data["limit"] = zone_element.limit
            if hasattr(zone_element, "offset"):
                data["offset"] = zone_element.offset
            if hasattr(zone_element, "is_hero_image"):
                data["is_hero_image"] = zone_element.is_hero_image
            if hasattr(zone_element, "large_lead_story"):
                data["large_lead_story"] = zone_element.large_lead_story
            if hasattr(zone_element, "flip_story_display"):
                data["flip_story_display"] = zone_element.flip_story_display
            if hasattr(zone_element, "label"):
                data["label"] = zone_element.label
            if hasattr(zone_element, "url_params"):
                data["url_params"] = zone_element.url_params
            if hasattr(zone_element, "list_type"):
                data["list_type"] = zone_element.list_type

            if is_sports_results_stories:
                data["stories"] = context.get("sport_results_stories", [])
            else:
                data["stories"] = zone_stories

            if hasattr(zone_element, "popular_story_list"):
                data["popular_stories"] = zone_element.popular_stories(
                    limit=4,
                    use_cache=not is_editmode,
                )

            data["dpe_data"] = None

            if dpe_id := getattr(zone_element, "dpe_id", None):
                dpe_context = {
                    "conf": {
                        "dpefeature_dpe_id": dpe_id,
                        "dpefeature_dpe_publish_time": zone_element.dpe_publish_time,
                        "dpefeature_version": "v2",
                    }
                }

                issues = dpe_published_dates(
                    dpe_context, limit=1, dpe_id=dpe_id
                )
                if issues:
                    try:
                        first_issue = issues[0]
                        data["dpe_data"] = dpe_links(dpe_context, first_issue)
                    except IndexError:
                        pass

        else:
            data = model_to_dict(zone_element)

        if type(zone_element).__name__ == "ClassifiedList":
            element_classifieds = (
                zone_element.classifieds(use_cache=not is_editmode)
                if not is_editable
                else []
            )
            limit = zone_element.limit or CLASSIFIED_LIST_MAX_CLASSIFIEDS
            offset = zone_element.offset - 1
            element_classifieds = element_classifieds[offset : offset + limit]
            classified_list = getattr(zone_element, "classified_list", None)
            classified_list_pinned_classifieds_only = (
                classified_list.pinned_classifieds_only
                if classified_list
                else False
            )
            data["title"] = zone_element.title.strip()
            data["offset"] = zone_element.offset
            data["limit"] = zone_element.limit
            data["pinned_classifieds_only"] = (
                zone_element.pinned_classifieds_only
                or classified_list_pinned_classifieds_only
            )
            data["pinned_classified_ids"] = (
                classified_list.get_cleaned_pinned()
                if classified_list and not is_editable
                else []
            )
            data["total_classifieds"] = len(element_classifieds)
            data["classifieds"] = list(element_classifieds)

        if type(zone_element).__name__ == "ClusteredStoryList":
            data["from_organisation"] = zone_element.from_organisation
            page = getattr(zone_element, "page", {})
            data["url"] = getattr(page, "url", "")

        if type(zone_element).__name__ == "TitledStoryList":
            data["title"] = zone_element.title

        if type(zone_element).__name__ == "UGCList":
            element_ugc = (
                zone_element.ugc(use_cache=not is_editmode)
                if not is_editable
                else []
            )
            limit = zone_element.limit or UGC_LIST_MAX_ITEMS
            if zone_element.offset is None or zone_element.offset < 1:
                offset = 0
            else:
                offset = zone_element.offset - 1
            ugc_list = getattr(zone_element, "ugc_list", None)
            ugc_list_pinned_ugc_only = (
                ugc_list.pinned_ugc_only if ugc_list else False
            )
            data["ugc_list_id"] = ugc_list.id
            data["title"] = zone_element.title.strip()
            data["offset"] = zone_element.offset
            data["limit"] = zone_element.limit
            data["pinned_ugc_only"] = (
                zone_element.pinned_ugc_only or ugc_list_pinned_ugc_only
            )
            data["pinned_ugc_ids"] = (
                ugc_list.get_cleaned_pinned()
                if ugc_list and not is_editable
                else []
            )
            data["total_ugc"] = len(element_ugc)
            element_ugc = element_ugc[offset : offset + limit]
            data["ugc"] = list(element_ugc)

        if type(zone_element).__name__ == "MenuList":
            page = getattr(zone_element, "page", {})
            title = getattr(zone_element, "title", "").strip()
            data["name"] = title if title != "" else getattr(page, "name")
            data["url"] = getattr(page, "url")
            data["subtitle"] = getattr(zone_element, "subtitle", "")
            # TODO: Do we keep same template name check for full_page_render?
            if zone_element.template == "page_navigation.html":
                sub_pages = []
                child_pages = pull_sub_pages(page)
                if child_pages is not None:
                    for child_page in child_pages:
                        sub_pages.append(
                            {
                                "menu_name": child_page.menu_name,
                                "name": child_page.name,
                                "new_window": child_page.new_window,
                                "url": child_page.url,
                            },
                        )
                data["children"] = sub_pages

        if type(zone_element).__name__ == "Authors":
            data["authors"] = zone_element.author_collection.get_authors(
                include_latest_story=True,
            )

        if type(zone_element).__name__ == "Comments":
            data["comments"] = []
            for (
                content_uuid,
                content_container_uuid,
                story_id,
            ) in FeaturedComment.objects.filter(
                section_uuid=conf_settings["viafoura_section_uuid"]
            ).values_list(
                "content_uuid", "content_container_uuid", "story_id"
            )[:3]:
                story = get_story(story_id)
                page = page_for_story(story)
                data["comments"].append(
                    {
                        "container_uuid": content_container_uuid,
                        "id": content_uuid,
                        "story": {
                            **serialise_story_for_view(
                                story,
                                story_list=None,
                            ),
                            "page": page.name if page else None,
                        },
                    }
                )

        if type(zone_element).__name__ == "DPECard":
            if not features.dpefeature_enabled or not conf_settings.get(
                "dpefeature_dpe_id"
            ):
                continue

            # NOTE: If any DPE Card template require more issue dates, change the limit here.
            issues = dpe_published_dates(context, limit=1)
            if not issues:
                continue
            try:
                first_issue = issues[0]
            except IndexError:
                continue
            data["issues"] = issues
            data["first_issue_data"] = dpe_links(context, first_issue)

        if full_page_render:
            if type(zone_element).__name__ == "Navigation":
                # NOTE: To reduce DB queries:
                # - Skipping menu_tree for templates _currently_ known not to use it.
                # - cache menu_tree generation across zone items (eg. can be on nav header and footer).
                if (
                    zone_element.template != "shortcuts_strap.html"
                    or zone_element.template.startswith("footer")
                ):
                    if page_tree is None:
                        page_tree = get_page_tree(request)
                    data["menu_tree"] = page_tree
                # Shift shortcuts properties into central key for ease of use
                data["shortcuts"] = []
                shortcuts_fields = (
                    "shortcut_%s_label",
                    "shortcut_%s_description",
                    "shortcut_%s_url",
                    "shortcut_%s_icon",
                )

                shortcuts_optional_fields = (
                    "shortcut_%s_cta",
                    "shortcut_%s_new_window",
                    "shortcut_%s_badge",
                )

                for i in range(1, 5):
                    shortcut = {}

                    for field in shortcuts_fields + shortcuts_optional_fields:
                        model_field_name = field % i
                        field_data = data.pop(model_field_name, "")
                        field_name = field[12:]
                        shortcut[field_name] = field_data
                        data[model_field_name] = field_data

                    if all(
                        val
                        for key, val in shortcut.items()
                        if f"shortcut_%s_{key}"
                        not in shortcuts_optional_fields
                    ):
                        data["shortcuts"].append(shortcut)

                # Shift external links into central key for ease of use
                data["external_links"] = []
                external_links_fields = (
                    "external_link_%s_text",
                    "external_link_%s_url",
                    "external_link_%s_icon",
                )

                external_links_optional_fields = (
                    "external_link_%s_new_window",
                )

                for i in range(1, 6):
                    link = {}

                    for field in (
                        external_links_fields + external_links_optional_fields
                    ):
                        model_field_name = field % i
                        field_data = data.pop(model_field_name, "")
                        field_name = field[17:]
                        link[field_name] = field_data
                        data[model_field_name] = field_data

                    if all(
                        val
                        for key, val in link.items()
                        if f"external_link_%s_{key}"
                        not in external_links_optional_fields
                    ):
                        data["external_links"].append(link)

            if type(zone_element).__name__ == "DPEList":
                dpe_id = getattr(zone_element, "dpe_id", None)
                limit = getattr(zone_element, "limit", None)
                use_canonical_url = getattr(
                    zone_element, "use_canonical_url", False
                )

                if (
                    not features.dpefeature_enabled
                    or not conf_settings.get("dpefeature_version") == "v2"
                    or not (conf_settings.get("dpefeature_dpe_id") or dpe_id)
                ):
                    continue

                dpe_start_page = request.GET.get("dpe_start_page")

                issues = dpe_published_dates(
                    context,
                    dpe_id=dpe_id,
                    limit=limit,
                    start_date=dpe_start_page,
                )

                data["dpe_id"] = (
                    dpe_id
                    if dpe_id
                    else conf_settings.get("dpefeature_dpe_id")
                )
                if dpe_start_page:
                    data["start_date"] = dpe_start_page
                if limit is not None:
                    data["limit"] = limit
                data["use_canonical_url"] = use_canonical_url
                data["issues"] = [
                    {
                        **{"issue_date": issue},
                        **dpe_links(context, date_str=issue, dpe_id=dpe_id),
                    }
                    for issue in issues
                ]

            if type(zone_element).__name__ == "EMagList":
                if not features.emagsfeature_enabled:
                    data["issues"] = []
                    data["total_issues"] = 0
                    data["publication_filter_options"] = []
                    continue

                start_page = request.GET.get("start_page")
                limit = getattr(zone_element, "limit", 20)
                emag_type = request.GET.get("publication")
                order = (
                    "date"
                    if request.GET.get("order", "") == "oldest"
                    else "-date"
                )
                if start_page:
                    data["start_date"] = start_page
                if limit is not None:
                    data["limit"] = limit

                site = current_site()
                emags = EMagsDynamoDB(
                    emag_type=emag_type,
                    limit=limit,
                    order=order,
                    start_date=start_page,
                    site=site,
                )
                data["issues"] = emags.list()
                data["total_issues"] = emags.get_total()
                data["publication_filter_options"] = [
                    {"code": emag_type.name, "label": emag_type.title}
                    for emag_type in emags.get_available_emags()
                ]

            if type(zone_element).__name__ == "PageCollection":
                collection_pages = list(
                    zone_element.collection.collectionpage_set.select_related(
                        "page"
                    ).filter(page__accessible=True)
                )

                if zone_element.shuffle:
                    shuffle(collection_pages)

                data["pages"] = [
                    format_collection_page(
                        collection_page.page, zone_element.collection
                    )
                    for collection_page in collection_pages
                ]
                data["title"] = zone_element.collection.title

            if type(zone_element).__name__ == "StoryListCollection":
                collection_storylists = zone_element.collection.collectionstorylist_set.select_related(
                    "story_list"
                ).all()

                data["title"] = zone_element.collection.title
                data["storylists"] = []
                data["tags"] = zone_element.tags

                for collection_storylist in collection_storylists:
                    # Provide stories for the first storylist so it can
                    # be rendered with the initial html if needed
                    offset = 0
                    limit = zone_element.limit or STORY_LIST_MAX_STORIES
                    stories = collection_storylist.story_list.stories(
                        use_cache=not is_editmode,
                        extra_tags=zone_element.tags,
                    )
                    storylist_stories = stories[offset : offset + limit]
                    stories = add_story_attributes(
                        context,
                        storylist_stories,
                        StoryListEl(
                            story_list=collection_storylist.story_list,
                            limit=limit,
                            offset=offset,
                            use_canonical_url=zone_element.use_canonical_url,
                        ),
                        stories_for_bf=False,
                        use_canonical_url=zone_element.use_canonical_url,
                        show_canonical_site=False,
                    )
                    if stories:
                        data["storylists"].append(
                            {
                                "story_list_title": collection_storylist.story_list.title,
                                "story_list_id": collection_storylist.story_list.id,
                                "label": collection_storylist.label,
                                "stories": stories,
                            }
                        )

            if type(zone_element).__name__ == "SportsHub":
                data["sports"] = zone_element.sports

        if full_page_render:
            zone_section = "page" if zone_item.page_id else "global"
            zone_context = zone_context_data[zone_section]
        else:
            zone_context = zone_context_data

        if type(
            zone_element
        ).__name__ == "DailyMotion" and not is_zone_item_published(
            publish=zone_element.publish,
        ):
            last_item = zone_context[-1]
            if last_item["zone_item_type"] in ["heading", "menulist"]:
                zone_item_render = False
                pop_last_zone_item = True

        if pop_last_zone_item:
            zone_context.pop()

        if zone_item_render:
            zone_item_data = {
                "element_id": zone_element.id,
                "zone_item_id": zone_item.id,
                "zone_name": zone_item.zone,
                "zone_item_type": zone_item.element_type,
                "zone_item_data": data,
                "order": zone_item.order,
                "clientFetch": useClientFetch,
            }

            zone_context.append(zone_item_data)

    return zone_context_data


def get_react_context(request, context):
    page = context["page"]
    site = current_site()
    conf_settings = context.get("conf", {})
    features = conf_settings.get("features")
    name = conf_settings.get("name")
    is_editable = context.get("is_editable", False)

    # HACK: Don't get subscription prices for CT.
    # This is to mirror hack in Suzuka-UI where the
    # prices are hardcoded for CT.
    subs_prices = (
        {} if "The Canberra Times" in name else subscription_prices(context)
    )

    react_context = {
        "layout_theme": "Lego",
        "layout_template": page.template,
        "zone_items": context.get("zone_items", None),
        "settings": {
            "page_id": page.id,
            "site_id": site.id,
            "host": request.get_host(),
            "is_secure": request.is_secure(),
            "editable": is_editable,
            "view_type": context.get("view_type", ""),
            "static_url": context.get("STATIC_URL"),
            "static_site_url": context.get("STATIC_SITE_URL"),
            "transform_url": transform_host(context),
            "user_id": (
                getattr(request.user, "remote_user_id", None)
                if is_editable and request.user
                else None
            ),
        },
        "conf": {
            # misc
            "domain": conf_settings.get("domain"),
            "default_comment_feature": conf_settings.get(
                "default_comment_feature"
            ),
            "organization_id": site.settings.organization,
            "location": conf_settings.get("location"),
            "publication": conf_settings.get("publication"),
            "logo_main": logo_url(context),
            "name": name,
            # subscriptions
            "subscription_prices": subs_prices,
            # paywall
            "paywall": features.pianofeature_enabled,
            "piano_cta_variant": conf_settings.get(
                "pianofeature_piano_cta_variant"
            ),
            # mailchimp
            "mailchimp_enabled": features.mailfeature_enabled,
            "mailchimp_list_id": conf_settings.get("mailfeature_list_id"),
            "mailchimp_account_id": conf_settings.get("mailchimp_account_id"),
            # dpe
            "dpe_enabled": features.dpefeature_enabled,
            "dpe_version": conf_settings.get("dpefeature_version"),
            "dpe_host_non_proxy": getattr(settings, "DPE_HOST_V1_NON_PROXY"),
            "dpe_host": dpe_host(context),
            "dpe_id": conf_settings.get("dpefeature_dpe_id"),
            # business feature esov
            "bf_esov_enabled": features.bfesovfeature_enabled,
            # Polar
            "polar_enabled": features.polarfeature_enabled,
            # Facebook News
            "facebooknews_enabled": features.facebooknewsfeature_enabled,
            # Retently
            "retently_enabled": features.retentlyfeature_enabled,
            # Google Optimize
            "optimize_enabled": features.googleoptimizemanagerfeature_enabled,
            "optimize_experience": conf_settings.get(
                "googleoptimizemanagerfeature_experience"
            ),
            # Real Esate View
            "rev_org_id": settings.REALESTATE_VIEW_ORGANIZATION_ID,
        },
    }

    return react_context


def serialize_story_list(context, story_list, limit):
    stories = story_list.stories
    if callable(stories):
        stories = story_list.stories()

    story_dicts = []
    # TODO: fully handle paging
    for story in stories[:limit]:
        story_dict = json.loads(json.dumps(story, cls=StoryEncoder))
        story_dict["id"] = story.id
        story_dict["external_link"] = False
        story_dict["canonical_url"] = story.canonical_url
        story_dict["lead_image"] = story.lead_image
        story_dict["story_url"] = story_url_for(context, story)
        story_dicts.append(story_dict)

    return {
        "story_list_id": story_list.id,
        "pinned_story_ids": get_cleaned_pinned_stories(story_list),
        "stories": story_dicts,
        "template": "",
        "use_canonical_url": False,
    }


def conf_domain_for_render(context, domain):
    return f"www{domain[4:]}" if context.get("render_beta_as_www") else domain


def conf_name_for_render(context, name):
    return (
        name.replace("zBeta ", "")
        if context.get("render_beta_as_www")
        else name
    )


def get_react_context_v2(request: HttpRequest, context: dict) -> dict:
    assert isinstance(request, HasExperiment)
    site = current_site()
    is_ct = "canberratimes" in site.domain
    conf_settings = context.get("conf", {})
    features = conf_settings.get("features")
    name = conf_settings.get("name")
    page: Optional[Page] = cast(Optional[Page], context.get("page"))
    pages = context.get("pages")
    story: Optional[Story] = cast(Optional[Story], context.get("story"))
    story_template = context.get("story_template")
    layout_theme = conf_settings.get("theme_dir")
    story_layout = conf_settings.get("story_layout")
    optional_pages = site_optional_pages()
    mode = get_edit_mode(request)
    is_editable = context.get("is_editable", False)
    is_external_story = context.get("is_external_story", False)
    display_rev_notification = conf_settings.get(
        "display_rev_notification", False
    )

    if layout_theme:
        layout_theme = layout_theme.title()
    if story_template:
        layout_template = story_template
    elif page:
        layout_template = page.template
    else:
        layout_template = context.get("template_name", "")

    react_context = {
        "conf": {
            "brightcove_account_id": conf_settings.get(
                "brightcove_account_id"
            ),
            "brightcove_player_id": site.settings.brightcovefeature_player_id
            or conf_settings.get("brightcove_player_id"),
            "brightcove_adfree_player_id": conf_settings.get(
                "brightcove_adfree_player_id"
            ),
            "chartbeat_domain": conf_settings.get("chartbeat_domain"),
            "chartbeat_uid": conf_settings.get("chartbeat_uid"),
            "dailymotion_player_id": (
                conf_settings.get("dailymotionfeature_player_id") or ""
            ).strip()
            or settings.DEFAULT_DAILYMOTION_PLAYER_ID,
            "dailymotion_player_id_for_looper_videos": (
                conf_settings.get(
                    "dailymotionfeature_player_id_for_looper_videos"
                )
                or ""
            ).strip()
            or settings.DEFAULT_DAILYMOTION_PLAYER_ID_FOR_LOOPER_VIDEOS,
            "dailymotion_player_id_with_autoplay_disabled": (
                conf_settings.get("dailymotionfeature_player_id_no_autoplay")
            ).strip()
            or settings.DEFAULT_DAILYMOTION_PLAYER_ID_FOR_NO_AUTOPLAY,
            "dailymotion_default_playerlist_id": (
                conf_settings.get("dailymotionfeature_default_playerlist_id")
            ),
            "dailymotion_player_id_video_shorts": (
                conf_settings.get("dailymotionfeature_player_id_video_shorts")
            )
            or settings.DEFAULT_DAILYMOTION_PLAYER_ID_FOR_VIDEO_SHORTS,
            "dailymotion_player_id_for_explore_travel_articles": (
                conf_settings.get(
                    "dailymotionfeature_player_id_for_explore_travel_articles"
                )
                or ""
            ).strip()
            or settings.DEFAULT_DAILYMOTION_PLAYER_ID_FOR_EXPLORE_TRAVEL_ARTICLES,
            "dailymotion_syndication_key": settings.DAILYMOTION_SYNDICATION_KEY,
            "dailymotion_pip_enabled_mobile": conf_settings.get(
                "dailymotionfeature_pip_enabled_mobile", False
            ),
            "dailymotion_pip_enabled_desktop": conf_settings.get(
                "dailymotionfeature_pip_enabled_desktop", False
            ),
            "default_comment_feature": conf_settings.get(
                "default_comment_feature"
            ),
            "declustered": conf_settings.get("declustered"),
            "domain": conf_domain_for_render(
                context, conf_settings.get("domain")
            ),
            "use_clean_story_url": conf_settings.get("use_clean_story_url"),
            "facebook_url": conf_settings.get("facebook_url"),
            "ga_domain_1": conf_settings.get("ga_domain_1"),
            "ga_domain_2": conf_settings.get("ga_domain_2"),
            "ga_domain_3": conf_settings.get("ga_domain_3"),
            "ga_id_1": conf_settings.get("ga_id_1"),
            "ga_id_2": conf_settings.get("ga_id_2"),
            "ga_id_3": conf_settings.get("ga_id_3"),
            "google_ad_manager_network_identifier": settings.GOOGLE_AD_MANAGER_NETWORK_IDENTIFIER,
            "google_search_id": site.settings.google_search_id,
            "google_vertex_config_id": site.settings.google_vertex_config_id,
            "index_exchange_id": conf_settings.get("index_exchange_id"),
            "instagram_username": conf_settings.get("instagram_username"),
            "youtube_url": conf_settings.get("youtube_url"),
            "location": conf_settings.get("location"),
            "latitude": conf_settings.get("latitude"),
            "longitude": conf_settings.get("longitude"),
            "logo_alt": logo_url(context, "alt"),
            "logo_main": logo_url(context),
            **logo_svg_urls(context),
            "mode": mode,
            "name": conf_name_for_render(context, name),
            "organization_id": site.settings.organization,
            "publication": conf_settings.get("publication"),
            "twitter_username": conf_settings.get("twitter_username"),
            "has_puzzles": optional_pages["puzzles"],
            "has_gift": optional_pages["gift"],
            "has_group_subscriptions": optional_pages["group_subscriptions"],
            "has_dpe": optional_pages["dpe"],
            "has_beta": optional_pages["beta"],
            "top_down_ad_cat_targeting": conf_settings.get(
                "nav_top_down_ad_cat_targeting", False
            ),
            "mailchimp_list_manage": conf_settings.get(
                "mailchimp_list_manage"
            ),
            "domain_verify_code_google": conf_settings.get(
                "domain_verify_code_google"
            ),
            "domain_verify_code_facebook": conf_settings.get(
                "domain_verify_code_facebook"
            ),
            "domain_verify_code_bing": conf_settings.get(
                "domain_verify_code_bing"
            ),
            "rev_notification": {
                "enabled": display_rev_notification,
                "url": conf_settings.get("view_url") or "https://view.com.au/",
            },
            "display_ags_banner": conf_settings.get(
                "display_ags_banner", False
            ),
            "rev_organization_id": settings.REALESTATE_VIEW_ORGANIZATION_ID,
            "story_layout": story_layout,
            "timestamp": int(time.time() * 1000),
            "theme_variant": conf_settings.get("theme_variant"),
        },
        "layout_template": layout_template,
        "layout_theme": layout_theme,
        "theme_dir": site.settings.theme_dir,
        "settings": {
            "ab_test": request.ab_test,
            "editable": is_editable,
            "host": conf_domain_for_render(context, request.get_host()),
            "is_secure": request.is_secure(),
            "query_string": request.GET.urlencode(),
            "piano_api_url": settings.PIANO_API_URL,
            "piano_cdn_url": settings.PIANO_CDN_URL,
            "site_id": site.id,
            "static_site_url": context.get("STATIC_SITE_URL"),
            "static_url": context.get("STATIC_URL"),
            "transform_url": transform_host(context),
            "user_id": (
                getattr(request.user, "remote_user_id", None)
                if is_editable and request.user
                else None
            ),
            "view_type": context.get("view_type", ""),
            "author_piano_uids": (
                (get_story_author_piano_uids(story) or []) if story else []
            ),
        },
        "racetracks": {
            "monza_url": settings.MONZA_HOST,
            "phoenix_url": settings.PHOENIX_API_HOST,
            "silverstone_url": settings.SILVERSTONE_HOST,
            "sochi_url": f"https://{settings.SOCHI_HOST}/",
            "suzuka_url": settings.SUZUKA_HOST,
            "transform_url": transform_host(context),
            "valencia_url": settings.VALENCIA_HOST,
            "sepang_url": settings.SEPANG_HOST,
        },
        "zone_items": context.get("zone_items", None),
    }

    if sports_hub := context.get("sports_hub"):
        react_context["sports_hub"] = sports_hub

    if ugc := context.get("ugc"):
        react_context["ugc"] = {"ugc_detail": ugc}
        if recirc_sections := get_all_recirculation_sections(
            {
                "conf_settings": conf_settings,
                "features": features,
                "page": page,
                "static_url": cast(str, context.get("STATIC_URL", "")),
                "story_layout": story_layout,
                "current_ugc_id": ugc["id"],
            },
        ):
            react_context["ugc"]["recirculationSections"] = recirc_sections

    cluster = cluster_summary_for_site(site)
    if cluster:
        react_context["cluster"] = cluster

    if "token" in request.GET:
        react_context["access_token"] = request.GET.get("token")

    react_context["classifieds"] = {
        "categories": context.get("categories", ()),
        "ad": context.get("ad"),
        "ads": context.get("ads"),
        "category": context.get("category"),
        "cluster": context.get("cluster"),
        "cluster_ads": context.get("cluster_ads", ()),
        "clusters": context.get("clusters"),
        "primary_similar_ads_len": context.get("primary_similar_ads_len"),
        "side_bar_ads": context.get("side_bar_ads"),
        "similar_ads": context.get("similar_ads"),
        "stories": context.get("obituary_stories"),
        "subcategory": context.get("subcategory"),
        "query": context.get("query", ""),
    }

    if "author" in context:
        react_context["author"] = context["author"]
        react_context["zone_items"]["page"] = [
            {
                "zoneItemId": 10000,
                "zoneName": "main",
                "zoneItemType": "storylist",
                "zoneItemData": {
                    "pinnedStoryIds": [],
                    "template": "author-stories-listview.html",
                    "useCanonicalUrl": False,
                    "allowAds": False,
                    "pinnedStoriesOnly": False,
                    "limit": 10,
                    "offset": 1,
                    "isHeroImage": True,
                    "isPolar": False,
                    "largeLeadStory": True,
                    "flipStoryDisplay": True,
                    "stories": context["author_stories"],
                },
                "order": 0,
                "clientFetch": False,
            }
        ]

    if context.get("is_auction_home_page", False) and page and page.story_list:
        react_context["auctions"] = {
            "home": {
                "livestock_list": serialize_event_calendar_for_view(
                    context["livestock_list"], page.story_list
                ),
                "clearings_list": serialize_event_calendar_for_view(
                    context["clearings_list"], page.story_list
                ),
            },
        }

    if context.get("is_auction_list_page", False):
        has_previous_page = context["current_page"].has_previous()
        previous_page_number = (
            context["current_page"].previous_page_number()
            if has_previous_page
            else None
        )
        has_next_page = context["current_page"].has_next()
        next_page_number = (
            context["current_page"].next_page_number()
            if has_next_page
            else None
        )
        current_page = context["current_page"].number

        react_context["auctions"] = {
            "results": {
                "category": context["category_param_value"],
                "categoryOptions": [
                    option[0]
                    for option in context["category_param_options_home"]
                ],
                "location": context["location_param_value"],
                "locationOptions": [
                    option[0]
                    for option in context["location_param_options_home"]
                ],
                "sort": context["sort_param_value"],
                "species": context["species_param_value"],
                "speciesOptions": [
                    option[0]
                    for option in context["species_param_options_home"]
                ],
                "type": context["type_param_value"],
                "typeOptions": context["type_param_options"],
                "pagination": {
                    "itemCount": context["paginator"].count,
                    "currentPage": current_page,
                    "pageRange": list(context["paginator"].page_range),
                    "pageSize": context["page_size"],
                    "hasPreviousPage": has_previous_page,
                    "previousPageNumber": previous_page_number,
                    "hasNextPage": has_next_page,
                    "nextPageNumber": next_page_number,
                    "startIndex": context["current_page"].start_index(),
                    "endIndex": context["current_page"].end_index(),
                },
            },
        }

        if page and page.story_list:
            react_context["auctions"]["results"]["pagination"]["auctions"] = (
                serialize_event_calendar_for_view(
                    context["current_page"].object_list, page.story_list
                )
            )

    page_hierarchy: Optional[list[Page]] = None

    if page:
        is_fake_page = page.id is None
        # Minimum page data needed
        page_story_list = None
        if page.story_list:
            page_story_list = serialize_story_list(
                react_context,
                page.story_list,
                10,
            )

        react_context["page"] = {
            "double_click_cat": page.double_click_cat,
            "meta_title": page.meta_title,
            "name": page.name,
            "story_list": page_story_list,
            "no_index": page.no_index,
            "no_snippet": page.no_snippet,
            "template": page.template,
        }

        parent_page: Optional[Page] = None
        siblings: Optional[tuple[dict[str, Any], ...]] = None
        children: tuple[dict[str, Any], ...] = tuple()

        if not is_fake_page:
            page_hierarchy = get_page_hierarchy(page, site)
            parent_page = get_parent_from_page_hierarchy(page_hierarchy)

        if not story:
            if not is_fake_page:
                # Full page information when request is not a story

                @no_type_check
                def get_children() -> tuple[dict[str, Any], ...]:
                    # TODO: Created these functions with no_type_check decorator
                    # to disable type checking due issue causing crashing.
                    # - https://github.com/python/mypy/issues/17958
                    # - https://github.com/typeddjango/django-stubs/pull/2408

                    return tuple(
                        {
                            # Simplify keys from join
                            key.replace("page__", ""): value
                            for key, value in site_page.items()
                        }
                        for site_page in SitePage.objects.filter(
                            parent__page=page,
                            parent__site=site,
                            page__accessible=True,
                            page__menu_visible=True,
                        ).values(
                            "page__menu_name",
                            "page__name",
                            "page__new_window",
                            "page__url",
                            "page__meta_description",
                            "page__alt_menu_name",
                        )
                    )

                children = get_children()
                siblings_parent_pages = ["Puzzles"]
                if parent_page and parent_page.name in siblings_parent_pages:

                    @no_type_check
                    def get_siblings_excluding_page() -> tuple[
                        dict[str, Any], ...
                    ]:
                        return tuple(
                            {
                                key.replace("page__", ""): value
                                for key, value in site_page.items()
                            }
                            for site_page in SitePage.objects.filter(
                                parent__page=parent_page,
                                parent__site=site,
                                page__accessible=True,
                                page__menu_visible=True,
                            )
                            .exclude(page=page)
                            .values(
                                "page__menu_name",
                                "page__name",
                                "page__new_window",
                                "page__url",
                                "page__alt_menu_name",
                            )
                        )

                    siblings = get_siblings_excluding_page()

                if page.alt_menu_name != "" or (
                    parent_page and parent_page.show_siblings_on_child_pages
                ):

                    @no_type_check
                    def get_siblings() -> tuple[dict[str, Any], ...]:
                        return tuple(
                            {
                                key.replace("page__", ""): value
                                for key, value in site_page.items()
                            }
                            for site_page in SitePage.objects.filter(
                                parent__page=parent_page,
                                parent__site=site,
                                page__accessible=True,
                                page__menu_visible=True,
                            ).values(
                                "page__menu_name",
                                "page__name",
                                "page__new_window",
                                "page__url",
                                "page__alt_menu_name",
                            )
                        )

                    siblings = get_siblings()

            react_context["page"].update(
                {
                    "children": children,
                    "menu_name": page.menu_name,
                    "menu_visible": page.menu_visible,
                    "meta_description": page.meta_description,
                    "new_window": page.new_window,
                    "alt_menu_name": page.alt_menu_name,
                    "hide_rev_banner": page.hide_rev_banner,
                    "open_graph_description": page.open_graph_description,
                    "open_graph_image": page.open_graph_image,
                    "open_graph_title": page.open_graph_title,
                    "open_graph_type": page.open_graph_type,
                    "open_graph_url": page.open_graph_url,
                    "page_id": page.id,
                    "parent_page_id": parent_page.id if parent_page else 0,
                    "parent_page_name": parent_page.name
                    if parent_page
                    else None,
                    "parent_page_url": parent_page.url
                    if parent_page
                    else None,
                    "show_heading": page.show_heading,
                    "show_help": page.show_help,
                    "show_siblings_on_child_pages": page.show_siblings_on_child_pages,
                    "template_settings": page.template_settings,
                    "url": page.url,
                    "card_image": page.card_image.name
                    if page.card_image
                    else None,
                }
            )

            # Only add csrftoken to templates with forms, otherwise not needed
            if react_context["page"]["template"] in [
                "index_advertisement.html",
                "community/ugc_edit.html",
                "community/share_event.html",
                "community/share_photos.html",
                "community/share_story.html",
            ]:
                react_context["page"]["csrftoken"] = get_token(request)

            if siblings is not None:
                react_context["page"]["siblings"] = siblings

    elif story:
        page = page_for_story(story)

    if pages is None:
        page_props = {
            "double_click_cat",
            "menu_name",
            "name",
            "show_heading",
            "show_siblings_on_child_pages",
            "url",
            "id",
            "alt_menu_name",
        }
        if page and page.id is None:
            react_context["pages"] = [
                {prop: getattr(page, prop) for prop in page_props}
            ]
        else:
            pages = (
                page_hierarchy
                if page_hierarchy
                else get_page_hierarchy(page, site)
            )
            if pages:
                react_context["pages"] = [
                    {prop: getattr(page_, prop) for prop in page_props}
                    for page_ in pages
                ]
    else:
        react_context["pages"] = pages

    # Retrieve any relevant outage notifications for site
    filteredOutages = get_filtered_outages(page)
    if filteredOutages is not None:
        outages = []
        for outage in filteredOutages:
            data = {
                "id": outage.id,
                "name": outage.name,
                "message": mark_safe(outage.message),
                "message_type": outage.message_type,
            }
            if hasattr(outage, "url"):
                data["url"] = outage.url
            outages.append(data)
        react_context["outages"] = {"outages": outages}

    if story:
        is_sports_results_detail_page = context.get(
            "is_sports_results_detail_page", False
        )
        is_auction_detail_page = context.get("is_auction_detail_page", False)
        is_clearings_detail_page = context.get(
            "is_clearings_detail_page", False
        )
        is_helpcentre_detail_page = context.get(
            "is_helpcentre_detail_page", False
        )
        if is_auction_detail_page:
            react_context["story"] = serialise_auction_for_view(
                story,
                story_list=context.get("_story_list"),
                use_publish_from_utc=True,
            )
        elif is_clearings_detail_page:
            react_context["story"] = serialise_clearing_sale_for_view(
                story,
                story_list=context.get("_story_list"),
                use_publish_from_utc=True,
            )
        else:
            extra_kwargs = {}
            if is_helpcentre_detail_page:
                extra_kwargs.update(
                    {
                        "helpcentre_topic": context.get("helpcentre_topic"),
                        "story_slug": context.get("story_slug"),
                    }
                )

            react_context["story"] = serialise_story_for_view(
                story,
                story_list=context.get("_story_list"),
                site=site,
                use_publish_from_utc=True,
                is_external_story=is_external_story,
                **extra_kwargs,
            )
        try:
            react_context["story"]["keywords"] = context["story_keywords"]
        except KeyError:
            pass
        if hasattr(story, "body"):
            story_elements = [
                clean_story_element(element)
                for element in story.body["elements"]
            ]
            react_context["story"]["elements"] = story_elements
            react_context["story"]["seo_title"] = story.body.get(
                "title_seo", ""
            )
            react_context["story"]["seo_description"] = story.body.get(
                "seo_description", ""
            )
        if is_sports_results_detail_page or is_external_story:
            domain = current_site().domain
            url = f"https://{domain}{story.story_url}"
            react_context["story"]["canonical_url"] = url
            react_context["story"]["url"] = story.story_url
            react_context["story"]["story_url"] = story.story_url
            react_context["story"]["noTransformUrl"] = True

        # Add extra data GTM needs
        if features.googletagmanagerfeature_enabled:
            # Story organization
            try:
                story_org_id = react_context["story"]["organization"]
            except KeyError:
                pass
            else:
                if story_org_id:
                    story_org_name = ""
                    if not is_sports_results_detail_page:
                        org = get_org_data(story_org_id)
                        story_org_name = org.name if org else ""
                    react_context["story"]["org_name"] = story_org_name
            # Word count
            try:
                react_context["story"]["word_count"] = context["word_count"]
            except KeyError:
                pass
            try:
                react_context["story"]["word_count_range"] = context[
                    "word_count_range"
                ]
            except KeyError:
                pass

        react_context["story"]["is_opinion"] = context.get("is_opinion", False)
        react_context["story"]["content_tier"] = (
            "free"
            if (
                (is_ct and PAYWALL_CT_FREE_TAG in story.tags)
                or (not is_ct and PAYWALL_FREE_TAG in story.tags)
            )
            else ("locked" if "subscriber-only" in story.tags else "metered")
        )
        react_context["story"]["body_text"] = context.get("story_text", "")
        react_context["story"]["story_seo_no_index"] = (
            True if request.GET.get("cs") else False
        )

        if is_cartoon_instance(story):
            story_list_signpost = get_cartoon_storylist_signpost(story)
            story_list = get_first_story_list(story_list_signpost)
            if is_cartoon_instance(story_list):
                result = []
                for cartoon_story in story_list.stories():
                    story_dict = serialise_story_for_view(
                        cartoon_story, story_list=story_list, site=site
                    )
                    if has_lead_image(story_dict):
                        result.append(story_dict)

                react_context["story"]["storyList"] = {"stories": result}

                react_context["story"]["topicStories"] = get_topic_stories(
                    story
                )

        if story_layout == Settings.STORY_LAYOUT_ECHIDNA:
            react_context["story"]["topicStories"] = get_topic_stories(story)

        if recirc_sections := get_all_recirculation_sections(
            {
                "conf_settings": conf_settings,
                "features": features,
                "page": page,
                "static_url": cast(str, context.get("STATIC_URL", "")),
                "story_layout": story_layout,
            },
        ):
            react_context["story"]["recirculationSections"] = recirc_sections

        if is_businessfeature_instance(story):
            story_list_title = get_businessfeature_titles(story)
            story_list = get_first_story_list(story_list_title)
            if is_businessfeature_instance(story_list):
                result = []
                for bf_story in story_list.stories(
                    limit=STORY_LIST_MAX_STORIES
                ):
                    story_dict = serialise_story_for_view(
                        bf_story, story_list=story_list, site=site
                    )
                    try:
                        bf_story_detail = get_story(bf_story.id)
                    except SlumberBaseException:
                        continue
                    if hasattr(bf_story_detail, "body"):
                        story_dict["elements"] = bf_story_detail.body[
                            "elements"
                        ]
                        story_dict["seo_title"] = bf_story_detail.body.get(
                            "title_seo", ""
                        )
                    label_dict = get_businessfeature_labels(bf_story)
                    if label_dict is not None:
                        bf_label = label_dict["title"]
                        story_dict["bfStoryListLabel"] = bf_label
                    result.append(story_dict)

                story_list_label = None
                story_list_label_dict = get_businessfeature_labels(story_list)
                if story_list_label_dict is not None:
                    story_list_label = story_list_label_dict["title"]
                react_context["story"]["storyList"] = {
                    "stories": result,
                    "storyListTitle": story_list.title,
                    "storyListLabel": story_list_label,
                }

        local_news_story_list = (
            StoryList.objects.filter(title=f"{site.name} Local News")
            .order_by("-id")
            .first()
        )

        disable_storypage_rhs_storylist = conf_settings.get(
            "disable_story_page_rhs_storylist"
        )

        if local_news_story_list and not disable_storypage_rhs_storylist:
            react_context["story"]["local_news_story_list"] = (
                serialize_story_list(
                    react_context,
                    local_news_story_list,
                    6,
                )
            )

        cartoon_of_the_day_story_list = (
            StoryList.objects.filter(title=f"{site.name} - Cartoon of the day")
            .order_by("-id")
            .first()
        )

        if cartoon_of_the_day_story_list:
            react_context["story"]["cartoon_of_the_day_story_list"] = (
                serialize_story_list(
                    react_context,
                    cartoon_of_the_day_story_list,
                    1,
                )
            )

        if features.googleextendedaccessfeature_enabled and conf_settings.get(
            "googleextendedaccessfeature_client_id"
        ):
            react_context["story"]["is_google_extended_access_article"] = (
                is_gnews_extended_access_article(request)
            )

        if "page" in react_context:
            # Add no_index for special cases of a story
            if not react_context["page"].get("no_index", False):
                react_context["page"]["no_index"] = should_noindex_story(story)

        # TODO: Possibly switch story_id to a char field to support
        # non numeric story_ids from external stories
        if not is_external_story:
            # Get two latest featured comments for a story
            featured_comments = FeaturedComment.objects.filter(
                section_uuid=conf_settings["viafoura_section_uuid"],
                story_id=story.id,
            ).values_list("content_uuid", "content_container_uuid")[:2]
            react_context["story"]["featured_comments"] = [
                {
                    "content_uuid": content_uuid,
                    "container_uuid": content_container_uuid,
                }
                for content_uuid, content_container_uuid in featured_comments
            ]

        dailymotion_video_ids = []

        if story_body := getattr(story, "body", None):
            for element in story_body["elements"]:
                if service_id := element.get("service_id"):
                    dailymotion_video_ids.append(service_id)

        if dailymotion_video_count := len(dailymotion_video_ids):
            react_context["story"]["dailymotion_video_count"] = (
                dailymotion_video_count
            )

            react_context["story"]["dailymotion_video_ids"] = (
                dailymotion_video_ids
            )

    feature_data = {}

    # Feature: Community Recirculation
    feature_data["community_recirculation"] = {
        "enabled": features.communityrecirculationfeature_enabled,
    }

    # Feature: User Bookmarks
    feature_data["user_bookmarks"] = {
        "enabled": features.userbookmarksfeature_enabled,
    }

    # Feature: Ads
    feature_data["ad_serving"] = {
        "enabled": features.adservingfeature_enabled,
    }
    if features.adservingfeature_enabled:
        feature_data["ad_serving"]["data"] = {
            "aps_publisher_id": site.settings.adservingfeature_aps_publisher_id,
            "auto_refresh_interval": site.settings.adservingfeature_auto_refresh_interval,
            "bottom_anchor_ad_position": site.settings.adservingfeature_bottom_anchor_ad_position,
            "double_click_site": site.settings.double_click_site,
            "double_click_zone": site.settings.double_click_zone,
            "double_click_cat": site.settings.double_click_cat,
            "double_click_region": site.settings.double_click_region,
            "double_click_state": site.settings.double_click_state,
            "enable_lazy_load_ab_test": site.settings.adservingfeature_enable_lazy_load_ab_test,
            "fetch_margin_percent": site.settings.adservingfeature_fetch_margin_percent,
            "mobile_scaling": site.settings.adservingfeature_mobile_scaling,
            "render_margin_percent": site.settings.adservingfeature_render_margin_percent,
            "use_bottom_anchor_ad": site.settings.adservingfeature_use_bottom_anchor_ad,
            "use_mantis": site.settings.adservingfeature_use_mantis,
            "use_publift": site.settings.adservingfeature_use_publift,
            "fuse_library_version": site.settings.adservingfeature_fuse_library_version,
        }

    # Lazy load Ads
    feature_data["ad_lazy_load_gpt"] = {
        "enabled": features.adslazyloadmanagerfeature_enabled,
    }

    # Feature: Piano
    feature_data["piano"] = {
        "enabled": (features.pianofeature_enabled and mode != "editmode")
    }
    if feature_data["piano"]["enabled"]:
        paywall_campaign = PaywallCampaign.objects.for_site(site)  # type: ignore[attr-defined]
        feature_data["piano"]["data"] = {
            "aid": conf_settings.get("pianofeature_piano_aid"),
            "beta_resource_id": conf_settings.get(
                "pianofeature_piano_beta_resource_id"
            ),
            "cta_variant": conf_settings.get("pianofeature_piano_cta_variant"),
            "complete_profile_enrichments": conf_settings.get(
                "pianofeature_piano_complete_profile_enrichments"
            ),
            "header": conf_settings.get("pianofeature_piano_sub_header"),
            "hide_subscriber_signposts": conf_settings.get(
                "pianofeature_hide_subscriber_signposts"
            ),
            "enterprise_subscriptions": [
                {
                    "icon_url": es.enterprise_subscription.icon_url,
                    "name": es.enterprise_subscription.name,
                    "piano_connection_name": es.enterprise_subscription.piano_connection_name,
                    "my_account_logo_path": es.enterprise_subscription.my_account_logo_path,
                }
                for es in EnterpriseSubscriptionSite.objects.get_active_enterprise_subscriptions_for_site()  # type: ignore[attr-defined]
            ],
            "article_paywall_heading_text": conf_settings.get(
                "pianofeature_article_paywall_heading_text"
            ),
            "hide_article_annual_savings_pill": conf_settings.get(
                "pianofeature_hide_article_annual_savings_pill"
            ),
            "registration_only": conf_settings.get(
                "pianofeature_registration_only"
            ),
            "site_id": conf_settings.get("pianofeature_piano_site_id"),
            "support_auth_server_paywall": conf_settings.get(
                "pianofeature_piano_support_auth_server_paywall"
            ),
            "support_login_apple": conf_settings.get(
                "pianofeature_piano_support_login_apple"
            ),
            "support_login_facebook": conf_settings.get(
                "pianofeature_piano_support_login_facebook"
            ),
            "support_login_google": conf_settings.get(
                "pianofeature_piano_support_login_google"
            ),
            "sub_colour": conf_settings.get("pianofeature_piano_sub_colour"),
            "sub_header": conf_settings.get(
                "pianofeature_piano_sub_subheader"
            ),
            "support_premium_subscription": conf_settings.get(
                "pianofeature_piano_support_premium_subscription"
            ),
            "support_premium_extended": conf_settings.get(
                "pianofeature_piano_support_premium_extended"
            ),
            "has_social_screen": conf_settings.get(
                "pianofeature_social_media"
            ),
            "is_premium_request": is_premium_request(request),
            "is_recommendation_content_enabled": conf_settings.get(
                "pianofeature_enable_piano_recommendation_content"
            ),
            "is_fullwidth_recommendation_content_enabled": conf_settings.get(
                "pianofeature_enable_piano_recommendation_content_fullwidth"
            ),
            "support_print_bundle": conf_settings.get(
                "pianofeature_piano_support_print_bundle"
            ),
            "is_ab_testing": conf_settings.get(
                "pianofeature_enable_piano_a_b_test"
            ),
            "support_monthly_annual_paywall": conf_settings.get(
                "pianofeature_piano_support_monthly_annual_paywall"
            ),
            "paywall_campaign": None
            if not paywall_campaign
            else {
                "name": paywall_campaign.name,
                "lead_text": paywall_campaign.lead_text,
                "term_display": paywall_campaign.term_display,
                "term_focus": paywall_campaign.term_focus,
                "pill_text": paywall_campaign.pill_text,
                "pill_colour": paywall_campaign.pill_colour,
                "pill_text_colour": paywall_campaign.pill_text_colour,
                "pill_position": paywall_campaign.pill_position,
            },
        }

    # Feature: Mail
    feature_data["mail"] = {
        "enabled": features.mailfeature_enabled,
    }
    if features.mailfeature_enabled:
        provider_setting = conf_settings.get("mailfeature_provider")
        provider = PROVIDERS[provider_setting]
        feature_data["mail"]["data"] = {
            "provider": provider_setting,
            "marketing_cloud_url": conf_settings.get(
                "mailfeature_marketing_cloud_url"
            ),
            "mailchimp_account_id": getattr(provider, "account_id", None),
            "mailchimp_list_manage_url": getattr(
                provider, "list_manage_url", None
            ),
            "list_id": conf_settings.get("mailfeature_list_id"),
            "support_newsletters_landing_page": conf_settings.get(
                "mailfeature_support_newsletters_landing_page"
            ),
        }
        if conf_settings.get("mailfeature_support_newsletters_landing_page"):
            feature_data["mail"]["data"]["article_widget_heading"] = (
                conf_settings.get("mailfeature_article_widget_heading")
            )
            feature_data["mail"]["data"]["article_widget_url"] = (
                conf_settings.get("mailfeature_article_widget_url")
            )
            feature_data["mail"]["data"]["newsletters"] = {
                "local": get_remote_regional_mail_groups(site),
                "national": get_remote_national_mail_groups(site),
            }
            feature_data["mail"]["data"][
                "support_newsletters_subscribe_flow"
            ] = conf_settings.get(
                "mailfeature_support_newsletters_subscribe_flow"
            )

    # Feature: Digital Print Edition
    feature_data["dpe"] = {
        "enabled": features.dpefeature_enabled,
    }
    if features.dpefeature_enabled:
        feature_data["dpe"]["data"] = {
            "dpe_id": conf_settings.get("dpefeature_dpe_id"),
            "host": dpe_host(context),
            "version": conf_settings.get("dpefeature_version"),
            "dpe_howto_video_id": conf_settings.get(
                "dpefeature_dpe_howto_video_id"
            ),
            "dpe_index_page_url": get_dpe_index_page_url(site),
        }

        issues = dpe_published_dates(context)
        if len(issues) > 0:
            feature_data["dpe"]["data"]["dpe_latest_date"] = issues[0]

    # Feature: Business Features ESOV
    feature_data["bf_esov"] = {
        "enabled": features.bfesovfeature_enabled,
    }

    # Feature: Polar
    feature_data["polar"] = {
        "enabled": features.polarfeature_enabled,
    }

    # Feature: Taboola
    feature_data["taboola"] = {
        "enabled": features.taboolafeature_enabled,
    }
    if features.taboolafeature_enabled:
        feature_data["taboola"]["data"] = {
            "publisher_id": conf_settings.get(
                "taboolafeature_publisher_id", ""
            )
        }

    # Feature: HindSight
    feature_data["hind_sight"] = {
        "enabled": features.hindsightfeature_enabled,
    }

    # Feature: Tooltips
    feature_data["tooltips"] = {
        "enabled": features.tooltipsfeature_enabled,
    }

    # Feature: Teads
    feature_data["teads"] = {
        "enabled": features.teadsfeature_enabled,
    }

    # Feature: Weather
    feature_data["weather"] = {
        "enabled": features.weatherfeature_enabled,
    }

    # Feature: OwnLocal
    feature_data["own_local"] = {
        "enabled": features.ownlocalfeature_enabled,
    }
    if features.ownlocalfeature_enabled:
        feature_data["own_local"]["data"] = {
            "has_directory": conf_settings["ownlocalfeature_has_directory"],
            "partner_id": conf_settings["ownlocalfeature_partner_id"],
        }

    feature_data["oovvuu"] = {
        "enabled": features.oovvuufeature_enabled,
    }

    # Feature: Puzzle
    feature_data["puzzle"] = {
        "enabled": features.puzzlefeature_enabled,
    }
    if features.puzzlefeature_enabled:
        feature_data["puzzle"]["data"] = {
            "logo": conf_settings["puzzlefeature_logo"],
            "subscribers_only": conf_settings[
                "puzzlefeature_subscribers_only"
            ],
            "crossword": conf_settings["puzzlefeature_crossword"],
            "sudoku": conf_settings["puzzlefeature_sudoku"],
            "ultimate_trivia": conf_settings["puzzlefeature_ultimate_trivia"],
            "cryptic_crossword": conf_settings[
                "puzzlefeature_cryptic_crossword"
            ],
            "word_search": conf_settings["puzzlefeature_word_search"],
            "code_cracker": conf_settings["puzzlefeature_code_cracker"],
            "wheel_words": conf_settings["puzzlefeature_wheel_words"],
        }

    # Feature: Real Estate
    feature_data["real_estate"] = {
        "enabled": features.realestatefeature_enabled,
    }

    if features.realestatefeature_enabled:
        feature_data["real_estate"]["data"] = {
            "exclusive_homes_api_url": settings.EXCLUSIVE_HOMES_API_URL,
            "exclusive_widget_enabled": conf_settings.get(
                "realestatefeature_exclusive_widget_enabled", False
            ),
            "query_data": conf_settings.get(
                "realestatefeature_query_data", {}
            ),
            "utm_source": conf_settings.get(
                "realestatefeature_utm_source", ""
            ),
        }

    # Feature: Google Optimize Manager
    feature_data["google_optimize"] = {
        "enabled": features.googleoptimizemanagerfeature_enabled,
    }
    if features.googleoptimizemanagerfeature_enabled:
        feature_data["google_optimize"]["data"] = {
            "container_id": conf_settings.get(
                "googleoptimizemanagerfeature_container_id", ""
            ),
            "experience": conf_settings.get(
                "googleoptimizemanagerfeature_experience", ""
            ),
            "ga_id": conf_settings.get(
                "googleoptimizemanagerfeature_ga_id", ""
            ),
        }

    # Feature: Google Tag Manager
    feature_data["google_tag_manager"] = {
        "enabled": features.googletagmanagerfeature_enabled,
    }
    if features.googletagmanagerfeature_enabled:
        feature_data["google_tag_manager"]["data"] = {
            "container_id": conf_settings.get(
                "googletagmanagerfeature_container_id"
            ),
            "env_vars": conf_settings.get("googletagmanagerfeature_env_vars"),
            "measurement_id": conf_settings.get(
                "googletagmanagerfeature_measurement_id"
            ),
        }

    # Feature: Google Extended Access
    feature_data["google_extended_access"] = {
        "enabled": features.googleextendedaccessfeature_enabled,
    }
    if features.googleextendedaccessfeature_enabled:
        feature_data["google_extended_access"]["data"] = {
            "client_id": conf_settings.get(
                "googleextendedaccessfeature_client_id"
            ),
        }

    # Feature: Disqus
    feature_data["disqus"] = {
        "enabled": features.disqus_enabled,
    }
    if features.disqus_enabled:
        feature_data["disqus"]["data"] = {
            "shortname": conf_settings.get("disqus_shortname", ""),
        }

    # Feature: HotJar
    feature_data["hotjar"] = {
        "enabled": features.hotjarfeature_enabled,
    }
    if features.hotjarfeature_enabled:
        feature_data["hotjar"]["data"] = {
            "id": conf_settings["hotjarfeature_id"],
        }

    # Feature: Mobile App
    feature_data["mobile_app"] = {
        "enabled": features.mobileappfeature_enabled,
    }
    if features.mobileappfeature_enabled:
        feature_data["mobile_app"]["data"] = {
            "app_store_id": conf_settings.get("mobileappfeature_app_store_id"),
            "google_play_id": conf_settings.get(
                "mobileappfeature_google_play_id"
            ),
            "smart_banner": conf_settings.get("mobileappfeature_smart_banner"),
            "smart_banner_app_header": conf_settings.get(
                "mobileappfeature_smart_banner_app_header"
            ),
            "app_ios_price_info": conf_settings.get(
                "mobileappfeature_app_ios_price_info"
            ),
            "app_android_price_info": conf_settings.get(
                "mobileappfeature_app_android_price_info"
            ),
            "branch_io_public_key": conf_settings.get(
                "mobileappfeature_branch_io_public_key", ""
            ).strip(),
        }

    # Feature: Airship Push Notifications
    feature_data["push_notifications"] = {
        "enabled": features.pushnotificationfeature_enabled
    }
    if features.pushnotificationfeature_enabled:
        feature_data["push_notifications"]["data"] = {
            "app_key": conf_settings.get("pushnotificationfeature_app_key"),
            "app_token": conf_settings.get(
                "pushnotificationfeature_app_token"
            ),
            "vapid_public_key": conf_settings.get(
                "pushnotificationfeature_vapid_public_key"
            ),
            "website_push_id": conf_settings.get(
                "pushnotificationfeature_website_push_id"
            ),
            "notification_icon": conf_settings.get(
                "pushnotificationfeature_notification_icon"
            ),
            "display_threshold": conf_settings.get(
                "pushnotificationfeature_display_threshold"
            ),
            "ask_again": conf_settings.get(
                "pushnotificationfeature_ask_again"
            ),
        }

    # Feature: Chartbeat Headline Testing
    feature_data["headline_testing"] = {
        "enabled": features.headlinetestfeature_enabled,
    }

    # Feature: Facebook News
    feature_data["facebook_news"] = {
        "enabled": features.facebooknewsfeature_enabled,
    }
    if features.facebooknewsfeature_enabled:
        feature_data["facebook_news"]["data"] = {
            "app_id": conf_settings.get("facebooknewsfeature_app_id"),
            "scope": conf_settings.get("facebooknewsfeature_scope"),
            "automatic": conf_settings.get("facebooknewsfeature_automatic"),
        }

    # Feature: Retently
    feature_data["retently"] = {
        "enabled": features.retentlyfeature_enabled,
    }
    if features.retentlyfeature_enabled:
        feature_data["retently"]["data"] = {
            "track_revoked_access": conf_settings.get(
                "retentlyfeature_track_revoked_access"
            ),
            "track_recent_signups": conf_settings.get(
                "retentlyfeatyre_track_recent_signups"
            ),
        }

    # Feature: Business Profiles
    feature_data["business_profiles"] = {
        "enabled": features.businessfeature_enabled,
    }
    if features.businessfeature_enabled:
        feature_data["business_profiles"]["data"] = {
            "location": conf_settings.get("businessfeature_location"),
            "radius": conf_settings.get("businessfeature_radius"),
            "widget_position": conf_settings.get(
                "businessfeature_widget_position"
            ),
        }

    # Feature: Microsoft Clarity
    feature_data["microsoft_clarity"] = {
        "enabled": features.microsoftclarityfeature_enabled,
    }
    if features.microsoftclarityfeature_enabled:
        feature_data["microsoft_clarity"]["data"] = {
            "clarity_id": conf_settings["microsoftclarityfeature_clarity_id"],
        }

    # Feature: Retently
    feature_data["retently"] = {
        "enabled": features.retentlyfeature_enabled,
    }

    # Feature: Roy Morgan Analytics
    feature_data["roy_morgan_analytics"] = {
        "enabled": features.roymorgananalyticsfeature_enabled,
    }
    if features.roymorgananalyticsfeature_enabled:
        feature_data["roy_morgan_analytics"]["data"] = {
            "client_id": conf_settings["roymorgananalyticsfeature_client_id"],
            "website_id": conf_settings[
                "roymorgananalyticsfeature_website_id"
            ],
            "publisher_id": conf_settings[
                "roymorgananalyticsfeature_publisher_id"
            ],
            "client_id_2": conf_settings[
                "roymorgananalyticsfeature_client_id_2"
            ],
            "website_id_2": conf_settings[
                "roymorgananalyticsfeature_website_id_2"
            ],
            "publisher_id_2": conf_settings[
                "roymorgananalyticsfeature_publisher_id_2"
            ],
            "client_id_3": conf_settings[
                "roymorgananalyticsfeature_client_id_3"
            ],
            "website_id_3": conf_settings[
                "roymorgananalyticsfeature_website_id_3"
            ],
            "publisher_id_3": conf_settings[
                "roymorgananalyticsfeature_publisher_id_3"
            ],
        }

    # Feature: Viafoura
    feature_data["viafoura"] = {
        "enabled": features.viafoura_enabled,
    }
    if features.viafoura_enabled:
        feature_data["viafoura"]["data"] = {
            "comment_icon_test": conf_settings["viafoura_comment_icon_test"],
            "enable_syndication": conf_settings["viafoura_enable_syndication"],
            "enable_conversation_starter": conf_settings[
                "viafoura_enable_conversation_starter"
            ],
            "section_uuid": conf_settings["viafoura_section_uuid"],
        }

    # Feature: EMags
    feature_data["emags"] = {
        "enabled": features.emagsfeature_enabled,
    }

    # Feature: LiveRamp ATS
    feature_data["liveramp"] = {
        "enabled": features.liverampfeature_enabled,
    }

    # Feature: IPSOS Iris Analytics
    feature_data["ipsos_iris_analytics"] = {
        "enabled": features.ipsosirisanalyticsfeature_enabled,
    }
    if features.ipsosirisanalyticsfeature_enabled:
        feature_data["ipsos_iris_analytics"]["data"] = {
            "homepage_section_id": conf_settings[
                "ipsosirisanalyticsfeature_homepage_section_id"
            ],
            "default_section_id": conf_settings[
                "ipsosirisanalyticsfeature_default_section_id"
            ],
            "news_section_id": conf_settings[
                "ipsosirisanalyticsfeature_news_section_id"
            ],
            "classifieds_section_id": conf_settings[
                "ipsosirisanalyticsfeature_classifieds_section_id"
            ],
            "jobs_section_id": conf_settings[
                "ipsosirisanalyticsfeature_jobs_section_id"
            ],
            "sport_section_id": conf_settings[
                "ipsosirisanalyticsfeature_sport_section_id"
            ],
            "comment_section_id": conf_settings[
                "ipsosirisanalyticsfeature_comment_section_id"
            ],
            "whatson_section_id": conf_settings[
                "ipsosirisanalyticsfeature_whatson_section_id"
            ],
        }

    # Feature: Photo Galleries settings
    feature_data["photo_gallery"] = {
        "enabled": features.photogalleryfeature_enabled,
    }
    if features.photogalleryfeature_enabled:
        setting_style = conf_settings[
            "photogalleryfeature_photogallery_choice"
        ]
        setting_layout = conf_settings[
            "photogalleryfeature_photogallery_layout_choice"
        ]
        gallery_layout = (
            setting_layout
            if [
                choice
                for choice in GALLERY_LAYOUT_CHOICES
                if choice[0] == setting_layout
            ]
            else DEFAULT_GALLERY_LAYOUT
        )
        gallery_style = (
            setting_style
            if setting_style in [style[0] for style in GALLERY_STYLES_CHOICES]
            else DEFAULT_GALLERY_STYLE
        )

        feature_data["photo_gallery"]["data"] = {
            "style": gallery_style,
            "layout": gallery_layout,
            "show_in_between_slide_ads": conf_settings[
                "photogalleryfeature_show_in_between_slide_ads"
            ],
            "ad_frequency": conf_settings["photogalleryfeature_ad_frequency"],
            "show_cta": conf_settings["photogalleryfeature_show_cta"],
            "cta_slide_title": conf_settings[
                "photogalleryfeature_cta_slide_title"
            ],
            "cta_slide_description": conf_settings[
                "photogalleryfeature_cta_slide_description"
            ],
            "cta_slide_button_text": conf_settings[
                "photogalleryfeature_cta_slide_button_text"
            ],
            "cta_persistent_button_text": conf_settings[
                "photogalleryfeature_cta_persistent_button_text"
            ],
            "cta_persistent_description": conf_settings[
                "photogalleryfeature_cta_persistent_description"
            ],
            "cta_url": conf_settings["photogalleryfeature_cta_url"],
        }

    # Feature: Async/Defer 3rd Party scripts
    feature_data["async_defer_third_party_scripts"] = {
        "enabled": features.asyncdeferthirdpartyscriptsfeature_enabled
    }

    # Feature: Sports Hub Sponsor Feature
    feature_data["sports_hub_sponsor"] = {
        "enabled": features.sportshubsponsorfeature_enabled,
    }
    if features.sportshubsponsorfeature_enabled:
        feature_data["sports_hub_sponsor"]["data"] = {
            "sponsor_data": conf_settings.get(
                "sportshubsponsorfeature_sponsor_data", {}
            ),
            "featured_sport_sponsor": conf_settings.get(
                "sportshubsponsorfeature_featured_sport_sponsor"
            ),
        }

    # Feature: Customer Data Platform
    feature_data["customer_data_platform"] = {
        "enabled": features.customerdataplatformfeature_enabled,
    }

    # Feature: Dailymotion Auto Pause Feature
    feature_data["dailymotion_auto_pause"] = {
        "enabled": features.dailymotionautopausefeature_enabled,
    }

    # Feature: ReCAPTCHA v3
    feature_data["recaptcha_v3"] = {
        "enabled": features.recaptchav3feature_enabled,
    }
    if features.recaptchav3feature_enabled:
        feature_data["recaptcha_v3"]["data"] = {
            "public_api_key": conf_settings.get(
                "recaptchav3feature_public_api_key", ""
            ),
        }

    # Feature: UGC
    feature_data["ugc"] = {
        "enabled": features.ugcfeature_enabled,
    }
    if features.ugcfeature_enabled:
        feature_data["ugc"]["data"] = {
            "use_region": conf_settings.get("ugcfeature_use_region", False),
            "show_share_story": conf_settings.get(
                "ugcfeature_show_share_story", False
            ),
            "cta_share_story": conf_settings.get(
                "ugcfeature_cta_share_story", ""
            ),
        }

    # Feature: AdFixus
    feature_data["ad_fixus"] = {
        "enabled": features.adfixusfeature_enabled,
    }
    if features.adfixusfeature_enabled:
        feature_data["ad_fixus"]["data"] = {
            "version": conf_settings.get("adfixusfeature_version", ""),
            "version_url": conf_settings.get("adfixusfeature_version_url", ""),
            "license_key": conf_settings.get("adfixusfeature_license_key", ""),
        }

    # Feature: Classifieds
    feature_data["classifieds"] = {
        "enabled": features.classifiedsfeature_enabled,
    }
    if features.classifiedsfeature_enabled:
        feature_data["classifieds"]["data"] = {
            "layout": conf_settings.get("classifiedsfeature_layout", 0),
            "show_similar_ads": conf_settings.get(
                "classifiedsfeature_show_similar_ads"
            ),
        }

    # Feature: Chaperone Site
    chaperone_enabled = has_chaperone_site(site.settings)
    feature_data["chaperone_site"] = {
        "enabled": chaperone_enabled,
    }
    if chaperone_enabled:
        parent_site = conf_settings.get("chaperonesitefeature_chaperone_site")
        feature_data["chaperone_site"]["data"] = {
            "id": parent_site.site_ptr_id,
            "domain": parent_site.domain,
            "name": parent_site.name,
        }

    # Feature: Community / Noticeboard
    feature_data["community_share_content"] = {
        "enabled": features.communityshareformfeature_enabled
    }

    # Feature: Skimlinks
    feature_data["skimlinks"] = {
        "enabled": features.skimlinksfeature_enabled,
    }

    if features.skimlinksfeature_enabled:
        feature_data["skimlinks"]["data"] = {
            "key": conf_settings.get("skimlinksfeature_key", ""),
            "is_story_blacklisted": (
                not is_external_story
                and story is not None
                and bool(story)
                and is_story_blacklisted_for_skimlinks(story.id)
            ),
        }

    # Feature: Explore Travel
    feature_data["explore_travel_recirculation"] = {
        "enabled": features.exploretravelrecirculationfeature_enabled,
    }

    if features.exploretravelrecirculationfeature_enabled:
        latest_story_widget_story_list = conf_settings.get(
            "exploretravelrecirculationfeature_latest_story_widget_story_list",
        )

        most_popular_story_widget_story_list = conf_settings.get(
            "exploretravelrecirculationfeature_most_popular_story_widget_story_list",
        )

        latest_story_limit = conf_settings.get(
            "exploretravelrecirculationfeature_latest_story_widget_story_limit",
            6,
        )
        most_popular_story_limit = conf_settings.get(
            "exploretravelrecirculationfeature_most_popular_story_widget_story_limit",
            10,
        )

        latest_story_widget_stories = []
        most_popular_story_widget_stories = []

        if latest_story_widget_story_list:
            latest_story_widget_stories = [
                serialise_story_for_view(
                    story, latest_story_widget_story_list, site=site
                )
                for story in latest_story_widget_story_list.stories(
                    use_cache=True, site_id=site.id, limit=latest_story_limit
                )
            ]

        if most_popular_story_widget_story_list:
            most_popular_story_widget_stories = [
                serialise_story_for_view(
                    story, most_popular_story_widget_story_list, site=site
                )
                for story in most_popular_story_widget_story_list.stories(
                    use_cache=True,
                    site_id=site.id,
                    limit=most_popular_story_limit,
                )
            ]

        feature_data["explore_travel_recirculation"]["data"] = {
            "latest_story_widget": {
                "stories": latest_story_widget_stories,
                "story_list_id": latest_story_widget_story_list.id
                if latest_story_widget_story_list
                else None,
                "limit": latest_story_limit,
            },
            "most_popular_story_widget": {
                "stories": most_popular_story_widget_stories,
                "story_list_id": most_popular_story_widget_story_list.id
                if most_popular_story_widget_story_list
                else None,
                "limit": most_popular_story_limit,
            },
        }

    # Norkon liveblog
    feature_data["norkon_liveblog"] = {
        "enabled": features.norkonliveblogfeature_enabled,
    }

    if features.norkonliveblogfeature_enabled:
        feature_data["norkon_liveblog"]["data"] = {
            "asset_version": conf_settings.get(
                "norkonliveblogfeature_asset_version", ""
            ),
            "api_url": settings.NORKON_API_URL,
            "base_url": settings.NORKON_BASE_URL,
            "websocket_url": settings.NORKON_WEBSOCKET_URL,
            "script_url": settings.NORKON_SCRIPT_URL,
            "tenant_key": settings.NORKON_TENANT_KEY,
        }

    # Feature: Supporter Site
    feature_data["supporter_site"] = {
        "enabled": features.supportersitefeature_enabled,
    }

    react_context["features"] = feature_data

    return react_context


def prepare_layout_context(request, context, **kwargs):
    # Merge conf context into current context
    full_page_render = context.get("full_page_render")
    context.update(conf(request))
    if context.get("disable_adserving"):
        if features := context.get("conf", {}).get("features"):
            features.adservingfeature_enabled = False

    # Detect if this is a beta site masquerading as a www site
    if is_show_beta(request) and current_site().domain.startswith("beta"):
        context["render_beta_as_www"] = True

    context["is_editable"] = is_editable(request)
    context["zone_items"] = prepare_zone_item_data(request, context, **kwargs)
    is_external_story = context.get("view_type") == "story" and isinstance(
        context.get("story"), DailymotionVideoStory
    )
    context["is_external_story"] = is_external_story
    if full_page_render:
        context["react_context"] = get_react_context_v2(request, context)
    else:
        context["react_context"] = get_react_context(request, context)
    prerender_payload = prerender_layout(request, context)
    context["headers"] = prerender_payload.get("headers", {})
    context["redirect"] = prerender_payload.get("redirect")
    context["rendered_react"] = prerender_payload.get("html", "")
    return context


def render_context_response(
    request: HttpRequest, context: dict, **kwargs
) -> HttpResponse:
    """Get a response from the renderer for the given context."""
    context = prepare_layout_context(request, context, **kwargs)

    if context.get("redirect"):
        return create_redirect_response(context)

    response = HttpResponse(context["rendered_react"])
    for k, v in context["headers"].items():
        if is_hop_by_hop(k):
            continue
        response[k] = v

    return response
