import json
import logging
import re
from collections import OrderedDict
from functools import reduce
from operator import or_
from random import randint
from typing import (
    Callable,
    Iterable,
    Literal,
    NamedTuple,
    Optional,
    Union,
    cast,
)

import boto3
from bleach.html5lib_shim import Filter
from bleach.sanitizer import Cleaner
from django.conf import settings
from django.contrib.sites.models import Site
from django.db.models import Q
from django.http import Http404, HttpRequest
from django.shortcuts import redirect, render
from django.template import loader
from django.utils import timezone
from django.utils.cache import patch_vary_headers

from suzuka.conf.sites import InvalidDomain, current_site
from suzuka.pages.constants import SKIMLINKS_STORY_BLACKLIST_BY_DOMAIN
from suzuka.pages.editing.utils import has_edit_perms
from suzuka.pages.mdetect import UAgentInfo
from suzuka.pages.models import Element, Page, SitePage, zone_item_cache_key
from suzuka.pages.monaco import suzuka_theme_api_request
from suzuka.stories import utils
from suzuka.stories.models import StoryList
from suzuka.stories.utils import (
    get_production_domain,
)
from suzuka.subscriptions.models import AuthorPianoUser

logger = logging.getLogger(__name__)

SCORES_AND_DRAWS_TEMPLATE = "scoresanddraws.html"

IFRAME_ALLOW_ATTRIB_VALUES = ("fullscreen", "geolocation")

CACHE_TIMEOUT_PAGE_HIERARCHY = 60 * 5  # 5 mins


class StoryListMatchResult(NamedTuple):
    story_list: StoryList
    tags: set[str]
    match_ratio: float


class StoryListMatchResultFull(NamedTuple):
    story_list: StoryList
    tags: set[str]
    match_ratio: float
    canonical_page: Optional[Page]


class EmbedFilter(Filter):
    def __iter__(self):
        for token in super().__iter__():
            if token["type"] in ["StartTag", "EmptyTag"] and token["data"]:
                tag = token["name"]
                for attr, value in token["data"].items():
                    if tag == "iframe" and attr[1] == "allow":
                        allow_directives = [
                            d
                            for d in (d_.strip() for d_ in value.split(";"))
                            if d in IFRAME_ALLOW_ATTRIB_VALUES
                        ]
                        token["data"][attr] = ";".join(allow_directives)
            yield token


def clean_story_element(element: dict) -> dict:
    if (
        element["type"] == "generic"
        and element.get("service") == "iframe"
        and (embed := element.get("embed"))
    ):
        cleaner = Cleaner(
            attributes={
                "iframe": (
                    "allow",
                    "allowfullscreen",
                    "height",
                    "src",
                    "style",
                    "width",
                )
            },
            filters=[EmbedFilter],
            strip=True,
            styles=["height"],
            tags=["iframe"],
        )
        element["embed"] = cleaner.clean(embed)
    return element


def primary_secondary_pages(page, site):
    if not page:
        return {}
    cache_key = f"primary-secondary-pages-{page.id}-{site.id}"
    results = utils.cache_get(cache_key)
    if results is not None:
        return results

    results = {"primary_page": page, "secondary_page": None}
    if site_page := page.site_page_for_site(site):
        if site_page.parent:
            results["primary_page"] = site_page.parent.page
            results["secondary_page"] = page
    else:
        logger.warning(
            f"Site page missing for <Page: {page.name}>[id={page.id}] and <Site: {site.name}>[id={site.id}]."
        )
    utils.cache_set(cache_key, results, CACHE_TIMEOUT_PAGE_HIERARCHY)
    return results


def get_page_hierarchy(page: Optional[Page], site: Site) -> list[Page]:
    """Get the page hierarchy for a given page and site as a top down flat page list.
    There is a max depth of `settings.MAX_PAGE_DEPTH`.

    Args:
        page (Optional[Page]): The target page.
        site (Site): The target site.

    Returns:
        list[Page]: The first page will be the root (non-parented) page with the last item the page itself.
    """
    if not page:
        return []
    cache_key = f"page-hierarchy-{page.id}-{site.id}"
    cached_hierarchy = cast(Optional[list[Page]], utils.cache_get(cache_key))
    if cached_hierarchy is not None:
        return cached_hierarchy

    site_page = page.site_page_for_site(site)

    hierarchy: list[Page] = []
    while site_page and len(hierarchy) < settings.MAX_PAGE_DEPTH:
        hierarchy.insert(0, site_page.page)
        try:
            site_page = (
                SitePage.objects.select_related("page").get(
                    id=site_page.parent_id
                )
                if site_page.parent_id
                else None
            )
        except SitePage.DoesNotExist:
            site_page = None

    utils.cache_set(cache_key, hierarchy, CACHE_TIMEOUT_PAGE_HIERARCHY)
    return hierarchy


def get_parent_from_page_hierarchy(
    page_hierarchy: list[Page],
) -> Optional[Page]:
    """Extract the parent page from the page hierarchy."""
    return page_hierarchy[-2] if len(page_hierarchy) > 1 else None


def page_for_request(request: HttpRequest, url: str) -> Page:
    """
    Return the ``Page`` object for the given request and URL.

    """
    site = current_site()
    pages = site.page_set.all()
    can_edit = has_edit_perms(request)
    if not can_edit:
        pages = pages.filter(draft=False, accessible=True)

    try:
        page = pages.prefetch_related("sitepage_set").get(url=url)
    except Page.DoesNotExist:
        if url != "/":
            raise Http404("Page at '%s' not found." % url)
        page = Page.objects.create_for_site(site, url=url, name="Front Page")

    return page


def normalise_tags(tags: Iterable[str]) -> set[str]:
    """Cleans up tags into a set we can use to do intersections on."""
    return set([tag.lower().strip() for tag in tags])


def page_for_video(story, site: Optional[Site] = None):
    """
    Determine page context for the dailymotion video story when no page
    context is available. For example, for video urls without subsection
    we cannot deduce the page context. For those videos we will use video
    tags as fallback mechanism to find page context.

    We look at the tags, and use them to get a list of possible story
    lists. We get story lists that contain any of the tags assigned to
    the story, then choose the story list with the closest set of matching
    tags.
    """

    return page_for_story(
        story,
        site=site,
        required_story_list_tags={"dailymotion"},
        filter_story_org=False,
    )


def page_for_story(
    story,
    story_list_id: Optional[int] = None,
    site: Optional[Site] = None,
    required_story_list_tags: Optional[set[str]] = None,
    filter_story_org: bool = True,
) -> Optional[Page]:
    """
    Determine page context for the story view when no page context is
    available. For example, story lists IDs are appended to story URLs,
    so that the story view can retrieve the story list's canonical
    page to use for the story view's page context. But canoncial pages
    are assigned based on the story list being used as the main story
    list for a page - so theoretically some story lists won't have a
    canonical page, which is where this function comes into play.

    So given a story in the story view, we try and determine which is
    the best story list it could possibly be a part of, since Suzuka
    has *no* knowledge of what stories are in what story lists.

    We look at the tags and org for the story, and use those to get a
    list of possible story lists. We get story lists that contain any
    of the tags assigned to the story, then choose the story list
    with the closest set of matching tags to the story.
    """

    if not site:
        site = current_site()
    if story_list_id:
        try:
            original_story_list = StoryList.objects.get(id=story_list_id)
            try:
                return Page.objects.filter(
                    story_list=original_story_list,
                    draft=False,
                    accessible=True,
                    sites=site,
                )[0]
            except IndexError:
                pass
        except StoryList.DoesNotExist:
            pass

    # If there are no tags on the story, we cannot deduce a meaningful
    # story list, so in this sad situation, we have no page context.
    if not story.tags:
        return None

    story_tags = normalise_tags(story.tags)

    # Story lists without an associated page aren't useful, so exclude those.
    story_list_qs = StoryList.objects.filter(
        storylistsite__site=site,
        storylistsite__canonical_page_id__isnull=False,
    )
    if filter_story_org and story.organization:
        # Get story lists for the org, or story lists that don't filter by org.
        orgs_filter = (
            Q(source_organization=story.organization)
            | Q(source_organization=None)
            | Q(source_organization="")
        )
        story_list_qs = story_list_qs.filter(orgs_filter)

    tags_filter = reduce(
        or_, [Q(tags__contains=tag) for tag in story_tags], Q()
    )
    story_lists = list(story_list_qs.filter(tags_filter))

    # Build a list of story lists with matching tags.

    story_list_tag_matches = get_matched_story_lists(
        story_lists, story_tags, required_story_list_tags
    )

    if not story_list_tag_matches:
        story_list_qs = StoryList.objects.filter(storylistsite__site=site)
        story_lists = list(story_list_qs.filter(tags_filter))
        story_list_tag_matches = get_matched_story_lists(
            story_lists, story_tags, required_story_list_tags
        )

    # Filter out story lists with draft or inaccessible canonical pages.

    story_list_canonical_pages: list[StoryListMatchResultFull] = [
        StoryListMatchResultFull(sl, tags, match_ratio, canonical_page)
        for sl, tags, match_ratio in story_list_tag_matches
        if (canonical_page := sl.get_canonical_page(site, linked_to_site=True))
        and not canonical_page.draft
        and canonical_page.accessible
        and not canonical_page.no_index
    ]

    if not story_list_canonical_pages:
        return None

    # Sort the story lists by most matching tags and canonical page name.

    story_list_canonical_pages = sorted(
        story_list_canonical_pages,
        key=cast(
            Callable[[StoryListMatchResultFull], tuple[int, float, str]],
            lambda item: (
                -len(item.tags),
                -item.match_ratio,
                item.canonical_page.name,
            ),
        ),
    )

    # Return the canonical page of the top match.

    return story_list_canonical_pages[0].canonical_page


def compare_tags(
    story_tags: Iterable[str], list_tags: str
) -> Union[Literal[False], set[str]]:
    _tags = normalise_tags(list_tags.split(","))
    tags = set(normalise_tags(story_tags))

    _and_tags = set(t[1:] for t in _tags if t[:1] in ("+", "&"))
    _not_tags = set(t[1:] for t in _tags if t[:1] in ("-", "!"))
    _or_tags = set(t for t in _tags if t[:1] not in ("+", "-", "&", "!"))

    matching_tags = tags & (_and_tags | _or_tags)
    if _or_tags and not (
        tags & _or_tags
    ):  # Story must match at least one OR tag.
        return False
    if tags & _not_tags:  # Story matches on a NOT tag.
        return False
    if not (tags >= _and_tags):  # Story doesn't contain all AND tags.
        return False
    return matching_tags


def get_matched_story_lists(
    story_lists: list[StoryList],
    tags: set[str],
    required_story_list_tags: Optional[set[str]] = None,
) -> list[StoryListMatchResult]:
    matched_story_lists: list[StoryListMatchResult] = []
    for story_list in story_lists:
        matching_tags = compare_tags(tags, story_list.tags)
        if not matching_tags:
            continue
        story_list_tags = normalise_tags(story_list.tags.split(","))

        if required_story_list_tags:
            matchable_tags = {tag.lstrip("+&") for tag in story_list_tags}
            if not all(
                required_tag in matchable_tags
                for required_tag in required_story_list_tags
            ):
                continue

        positive_tags = len(
            {tag for tag in story_list_tags if not tag.startswith(("-", "!"))}
        )
        matching_tags_ratio = (
            (len(matching_tags) / positive_tags) if positive_tags else 0
        )

        matched_story_lists.append(
            StoryListMatchResult(
                story_list, matching_tags, matching_tags_ratio
            )
        )

    return matched_story_lists


def cache_response_key(request):
    """
    Returns a cache key for the request.

    This replaces the default key function so the detected device
    can be included in the cache key. This is needed in order to
    serve correct values for the Titan data layer.

    """
    key_prefix = ""
    scheme = "https" if request.is_secure() else "http"
    host = request.get_host().lower()

    key_components = (
        key_prefix,
        request.method,
        scheme,
        host,
        request.get_full_path(),
        detect_device(request),
    )
    return "#".join(key_components)


catmap = {
    "homepage": "",
    "ent": "entertainment",
    "lands": "lifeandstyle",
}

page_catmap = {
    "lifeandstyle": "Life & Style",
    "aboutus": "About Us",
}


def varnish_device_type(request):
    """
    Returns the device type detected by Varnish.

    If `X-Varnish-Device-Type` request header is present and valid, the
    value of the header is returned. Otherwise, returns `None`.

    """
    device_type = request.META.get("HTTP_X_VARNISH_DEVICE_TYPE")

    if device_type in ["desktop", "mobile", "tablet"]:
        return device_type
    elif device_type in ["smartphone"]:
        return "mobile"


def akamai_device_type(request):
    """
    Returns the device type detected by Akamai.

    If `X-Cache-Device-Type` request header is present and valid, the
    value of the header is returned. Otherwise, returns `None`.

    """
    device_type = request.META.get("HTTP_X_CACHE_DEVICE_TYPE")

    if device_type in ["desktop", "mobile", "tablet"]:
        return device_type
    elif device_type in ["smartphone"]:
        return "mobile"


def detect_device(request):
    """
    Returns a string indicating the type of device.

    Uses the device type as detected by Varnish, if available. Otherwise,
    `pages.mdetect.UAgentInfo` is used to determine the device type.

    The returned value will be one of: desktop, mobile, tablet.

    """
    device_type = varnish_device_type(request)
    if device_type:
        return device_type

    device_type = akamai_device_type(request)
    if device_type:
        return device_type

    agent = UAgentInfo(
        userAgent=request.META.get("HTTP_USER_AGENT"),
        httpAccept=request.META.get("HTTP_ACCEPT"),
    )
    if agent.detectTierIphone():
        return "mobile"
    elif agent.detectTierTablet():
        return "tablet"
    elif agent.detectMobileQuick():
        return "mobile"

    return "desktop"


def get_device_type(request=None):
    """
    Ensure get correct device type
    """
    device = None
    if request is not None:
        device = akamai_device_type(request)
        if device is None:
            device = detect_device(request)
    return device


def build_ad_data_layer(request, context):
    data_layer = get_ad_data_layer_dict(context, request)

    return "<script>var titanEnabled=false,digitalData=%s</script>" % (
        json.dumps(data_layer, separators=(",", ":")),
    )


def get_ad_data_layer_dict(context, request):
    ad_args = context.get("ad_args")
    story = context.get("story")
    page = context.get("page")
    page_info = OrderedDict()
    try:
        page_info["author"] = story.byline
    except AttributeError:
        page_info["author"] = ""
    try:
        page_info["pageID"] = story.id
    except AttributeError:
        if request.path == "/":
            page_info["pageID"] = "homepage"
        else:
            page_info["pageID"] = request.path.lower()

    if request:
        page_info["sysEnv"] = detect_device(request)
    try:
        page_info["pageName"] = story.title
    except AttributeError:
        try:
            page_info["pageName"] = page.name
        except AttributeError:
            page_info["pageName"] = ""
    try:
        page_info["brand"] = "".join(current_site().name.split()).lower()
    except InvalidDomain:
        page_info["brand"] = ad_args["cat"]
    except AttributeError:
        pass
    page_info["generator"] = "newsnow"
    try:
        page_info["effectiveDate"] = story.updated_on.strftime(
            "%Y-%m-%dT%H:%M:%SZ"
        )
    except AttributeError:
        page_info["effectiveDate"] = ""
    try:
        page_info["issueDate"] = story.created_on.strftime(
            "%Y-%m-%dT%H:%M:%SZ"
        )
    except AttributeError:
        page_info["issueDate"] = ""
    cat_keys = ["cat", "cat1", "cat2", "cat3"]
    cats = [ad_args.get(cat, "") for cat in cat_keys]
    cats = [catmap.get(cat, cat) for cat in cats]
    try:
        cats.remove(page_info["pageID"])
    except ValueError:
        pass
    cat_dict = dict(list(zip(cat_keys, cats)))
    for cat in cat_keys:
        ad_args[cat] = cat_dict.get(cat, "")
    category = OrderedDict()
    try:
        category["primaryCategory"] = page_catmap.get(
            ad_args["cat1"], ad_args["cat1"].title()
        )
    except KeyError:
        category["primaryCategory"] = ""
    try:
        if category["primaryCategory"] != page.name:
            category["subCategory1"] = page.name
    except AttributeError:
        category["subCategory1"] = ""
    try:
        category["subCategory2"] = ad_args["cat3"]
    except KeyError:
        category["subCategory2"] = ""
    category["subCategory3"] = ""
    category["subCategory4"] = ""
    try:
        if (
            ad_args["ctype"] == "index"
            and category["primaryCategory"] in settings.INFO_PAGE_TYPES
        ):
            category["pageType"] = "Help and Info"
        else:
            category["pageType"] = ad_args["ctype"].title()
    except KeyError:
        category["pageType"] = ""
    if all(
        [
            not ad_args["cat1"],
            not ad_args["cat2"],
            not ad_args["cat3"],
            page_info["pageName"] == "Home",
        ]
    ):
        try:
            page_name = context["page"].name + " | " + current_site().name
        except KeyError:
            page_name = current_site().name

        ad_args["ctype"] = "homepage"
        page_info["pageName"] = page_name
        category["pageType"] = "Homepage"
    if any([ad_args["cat1"], ad_args["cat2"], ad_args["cat3"]]) and all(
        [not page_info["pageName"], category["pageType"] == "index"]
    ):
        try:
            page_name = context["page"].name + " | " + current_site().name
        except KeyError:
            page_name = current_site().name

        page_info["pageName"] = page_name
    page = OrderedDict()
    page["pageInfo"] = page_info
    page["category"] = category
    vendor = OrderedDict()
    profile = OrderedDict()
    profile["profileInfo"] = {"profileID": ""}
    profile["membershipType"] = "visitor"
    profile_list = [profile]
    user_list = [{"profile": profile_list}]
    cats = [_f for _f in cats if _f]
    try:
        cats.remove(page_info["pageID"])
    except ValueError:
        pass
    if story:
        ad_args["tags"] = story.tags
    ad_args.pop("sz", None)
    ad_args.pop("tile", None)
    ad_args.pop("pos", None)
    ad_args.pop("ord", None)
    ad_args.pop("cat3", None)
    if story:
        ad_tags = story.tags
        ad_args["tag"] = [s.replace("-", "") for s in ad_tags]
    ad_key_values = OrderedDict()
    for key in ad_args:
        if ad_args[key] and len(ad_args[key]) > 0:
            ad_key_values[key] = ad_args[key]
    titan = OrderedDict()
    titan["adSite"] = current_site().settings.double_click_site
    titan["adZone"] = "/".join(cats).strip("/")
    titan["adKeyValues"] = ad_key_values
    titan["adSlots"] = context.get(
        "react_titan_ads", context.get("titan_ad_slots", [])
    )
    data_layer = OrderedDict()
    data_layer["page"] = page
    data_layer["titan"] = titan
    data_layer["user"] = user_list
    data_layer["version"] = "1.0"
    data_layer["vendor"] = vendor
    return data_layer


def render_with_ad_data_layer(
    request, template_name, context=None, *args, **kwargs
):
    if context is None:
        context = {}

    context.update(
        {
            "double_click_ord": randint(10000000, 99999999),
            "titan_ads": {},
            "ad_args": {},
            "data_layer_context": {},
        }
    )

    response = render(request, template_name, context, *args, **kwargs)
    process_ad_tags(request, response, context)

    return response


def process_ad_tags(request, response, context):
    """
    Given page content, scan entire page for adspot pattern and dynamically number
    each aspots position based on size grouping. We also populate the titan datalayer
    adslots context key by including only non-conditional ads. Titan_sc holds all the ads
    that have been requested via a template tag, the position it requested it from and whether
    it is conditional or not.
    """
    titan_ad_slots = []
    titan_sc = context.get("titan_ads", {})
    starting_values = {"step": 1}
    ad_pattern = re.compile(r"adspot-(?P<size>[\dx_]+)(?:-pos(?P<pos>[\d]*))?")

    def pos_replace(matchobj):
        if matchobj.group("pos") == "":
            if starting_values["step"] <= 2:
                try:
                    ad = (
                        "adspot-"
                        + matchobj.group("size")
                        + "-pos"
                        + str(titan_sc[matchobj.group("size")][0][0])
                    )
                except (KeyError, IndexError):
                    return matchobj.group(0)

                if starting_values["step"] == 2:
                    try:
                        if (
                            titan_sc[matchobj.group("size")][0][1]
                            and ad in titan_ad_slots
                        ):
                            titan_ad_slots.remove(ad)
                    except (KeyError, IndexError):
                        pass
                    starting_values["step"] = 1
                    titan_sc.get(matchobj.group("size"), []).pop(0)
                else:
                    if matchobj.group("size") in titan_sc:
                        titan_ad_slots.append(ad)
                    starting_values["step"] += 1
                return ad

        else:
            if (
                matchobj.group("size") in titan_sc
                and matchobj.group() not in titan_ad_slots
            ):
                titan_ad_slots.append(matchobj.group())
            return matchobj.group()

    response.content = re.sub(
        ad_pattern, pos_replace, response.content.decode("utf-8")
    )
    context["titan_ad_slots"] = titan_ad_slots

    if "ad_args" in context and not context.get("data_layer_context", {}).get(
        "disabled"
    ):
        data_layer = build_ad_data_layer(request, context)

        if '"sysEnv"' in data_layer:
            if varnish_device_type(request):
                patch_vary_headers(response, ["X-Varnish-Device-Type"])
            elif akamai_device_type(request):
                patch_vary_headers(response, ["X-Cache-Device-Type"])
            else:
                patch_vary_headers(response, ["User-Agent"])

        response.content = response.content.replace(
            b"</head>", str.encode(data_layer + "</head>")
        )


def get_template_zones(template_name):
    template = loader.get_template(template_name).template
    zone_re = re.compile('\{%\s*zone\s*"(.*)"\s*as\s.*zone_items\s*%\}')

    try:
        with open(template.origin.name, "r") as tpl:
            return zone_re.findall(tpl.read())
    except (EnvironmentError, AttributeError):
        pass

    for path in template.engine.dirs:
        template_path = "%s/%s" % (path, template_name)
        try:
            with open(template_path, "r") as tpl:
                return zone_re.findall(tpl.read())
        except (EnvironmentError, AttributeError):
            pass

    return []


def has_multiauth(request, backends):
    for backend in backends:
        valid = backend.is_authenticated(request)

        if not valid:
            return False

    return True


def pull_sub_pages(parent_page, use_current_site=True):
    """
    Return a list containing the sub pages of the parent page
    """
    if isinstance(parent_page, Page):
        SUBPAGES_TIMEOUT = 60 * 60  # 1 hour
        cache_site_id = current_site().id if use_current_site else "all"
        sub_pages_cache_key = "sub-pages-{}-{}".format(
            cache_site_id, parent_page.id
        )

        sub_pages = utils.cache_get(sub_pages_cache_key)
        if sub_pages is None:
            filter_kwargs = {
                "parent__page": parent_page,
                "page__accessible": True,
            }
            if use_current_site:
                filter_kwargs["site"] = current_site()
            child_pages = (
                SitePage.objects.select_related("page")
                .filter(**filter_kwargs)
                .order_by("order")
            )
            sub_pages = [child_page.page for child_page in child_pages]
            utils.cache_set(sub_pages_cache_key, sub_pages, SUBPAGES_TIMEOUT)
        return sub_pages
    else:
        return None


def get_parent_page(page_id):
    """
    Return parent page by calling child page id
    """
    if page_id:
        try:
            return SitePage.objects.get(page__id=page_id).parent.page
        except (AttributeError, Page.DoesNotExist):
            pass


def pull_sibling_pages(page):
    """
    Return a list containing the sibling pages of the page
    """
    if isinstance(page, Page):
        SIBLING_PAGES_TIMEOUT = 60 * 60  # 1 hour
        sibling_pages_cache_key = "sibling-pages-{}-{}".format(
            current_site().id, page.id
        )
        sibling_pages = utils.cache_get(sibling_pages_cache_key)
        if sibling_pages is None:
            parent_page = get_parent_page(page.id)
            if parent_page is not None:
                childrenPages = SitePage.objects.select_related("page").filter(
                    parent__page=parent_page
                )
                sibling_pages = [
                    child_page.page for child_page in childrenPages
                ]
                utils.cache_set(
                    sibling_pages_cache_key,
                    sibling_pages,
                    SIBLING_PAGES_TIMEOUT,
                )
            else:
                return None
        return sibling_pages


class DynamoDBConnection:
    """
    Light wrapper for DynamoDB connection to a table.

    """

    def __init__(self, table_name):
        # Use these settings for dev only.
        # (local_settings.py)
        self.aws_access_key_id = getattr(settings, "AWS_ACCESS_KEY_ID", None)
        self.aws_secret_access_key = getattr(
            settings, "AWS_SECRET_ACCESS_KEY", None
        )
        # TODO: Seemed to need this session token too in local dev
        # self.aws_session_token = getattr(
        #     settings, 'AWS_SESSION_TOKEN', None)
        self.table_name = table_name

    @property
    def resource(self):
        if not hasattr(self, "_resource"):
            self._resource = boto3.resource(
                "dynamodb",
                region_name="ap-southeast-2",
                aws_access_key_id=self.aws_access_key_id,
                aws_secret_access_key=self.aws_secret_access_key,
                # TODO: Seemed to need this session token too in local dev
                # aws_session_token=self.aws_session_token
            )
        return self._resource

    @property
    def table(self):
        if not hasattr(self, "_table"):
            self._table = self.resource.Table(self.table_name)
        return self._table

    def put(self, item):
        return self.table.put_item(Item=item)

    def list(self, *args, **kwargs):
        options = {"Limit": 20}
        options.update(kwargs)
        return self.table.scan(**options)

    def query(self, *args, **kwargs):
        options = {
            "Limit": 20,
            "TableName": self.table_name,
            "ScanIndexForward": False,
        }
        options.update(kwargs)
        return self.table.query(**options)

    def count(self, *args, **kwargs):
        options = {
            "TableName": self.table_name,
            "Select": "COUNT",
        }
        options.update(kwargs)
        options.pop("Limit", None)
        options.pop("ExclusiveStartKey", None)
        return self.table.query(**options)


def get_theme_dir(site=None):
    """
    Returns site theme_dir value.
    """
    if site is None:
        site = current_site()

    return site.settings.theme_dir


def get_theme_choice(site=None):
    """
    Returns choice tuple of site's theme.
    """
    theme_dir = get_theme_dir(site)

    try:
        return [
            choice
            for choice in settings.THEME_CHOICES
            if choice[0] == theme_dir
        ][0]
    except IndexError:
        pass


def get_theme_templates(theme):
    """
    Fetch theme template list from Monaco.
    """
    response = suzuka_theme_api_request(f"{theme}")
    if response is None:
        return []
    return response


def get_global_zones(theme):
    """
    Fetch theme zones item template list from Monaco.
    """
    response = suzuka_theme_api_request(f"{theme}/globalZones")
    if response is None:
        return []
    return sorted(response)


def get_zone_item_templates(theme, zone_item_type):
    """
    Fetch theme zone item template list from Monaco.
    """
    response = suzuka_theme_api_request(f"{theme}/zoneitem/{zone_item_type}")
    if response is None:
        return []
    return response


def get_template_choices(theme_dir):
    """
    Return template choices for form fields sourced from Monaco.
    """
    theme_templates = get_theme_templates(theme_dir)

    templates_choices = [[it["key"], it["title"]] for it in theme_templates]
    return templates_choices


def get_zone_item_template_choices(theme_dir, zone_item_type):
    """
    Return zone item template choices for form fields sourced from Monaco.
    """
    theme_templates = get_zone_item_templates(theme_dir, zone_item_type)

    templates_choices = [[it["key"], it["title"]] for it in theme_templates]
    return templates_choices


def get_theme_template_zones(theme_dir, template):
    """
    Return all the theme template page zones as flat list.
    """
    theme_templates = get_theme_templates(theme_dir)

    found_template = next(
        (tpl for tpl in theme_templates if tpl["key"] == template), None
    )

    if not found_template or "zones" not in found_template:
        return []

    return found_template["zones"]


def get_dpe_index_page_url(site):
    try:
        site_page = SitePage.objects.select_related("page").get(
            page__url="digital-print-edition", site=site
        )
        return "/" + site_page.page.url.lstrip("/")
    except SitePage.DoesNotExist:
        pass

    # Fallback if `digital-print-edition` is not used as url
    try:
        site_page = SitePage.objects.select_related("page").get(
            page__name="Today's Paper", site=site
        )
    except SitePage.DoesNotExist:
        return ""

    if not site_page.page.url:
        return ""
    return "/" + site_page.page.url.lstrip("/")


def is_show_beta(request):
    return request.COOKIES.get("show_beta") == "true"


def is_premium_request(request):
    return request.COOKIES.get("_cb_pre_cookie_ref") == "true"


def get_edit_mode(request):
    return (
        "editmode"
        if request.GET.get("m", "normal") == "editmode"
        else "normal"
    )


def get_cached_zone_items(zone_items, filters):
    key = zone_item_cache_key(**filters)
    zone_items_cached = utils.cache_get(key)

    if zone_items_cached is None:
        element_types = [
            s.split(".")[1].lower() for s in list(Element.registry.keys())
        ]
        element_types.extend(["storylist__story_list", "menulist__page"])
        zone_items = zone_items.select_related(
            *element_types
        ).prefetch_related("menulist__page__sitepage_set")
        zone_items_cached = tuple(zone_items)
        utils.cache_set(key, zone_items_cached, 24 * 60 * 60)

    return zone_items_cached


def create_redirect_response(context):
    redirect_ctx = context.get("redirect")
    redirect_response = redirect(redirect_ctx.get("location"))
    redirect_response.status_code = redirect_ctx.get("status_code")
    return redirect_response


def is_zone_item_published(
    publish: list[str],
) -> bool:
    business_hours = {
        "from": 6,
        "to": 18,
    }
    now = timezone.now().astimezone(timezone.get_current_timezone())
    current_weekday = now.weekday()
    is_in_business_hours = (
        business_hours["from"] <= now.hour < business_hours["to"]
    )
    if not publish:
        return True
    if publish[current_weekday] == "0":
        return True
    if publish[current_weekday] == "1":
        return is_in_business_hours
    if publish[current_weekday] == "2":
        return not is_in_business_hours
    return False


def is_story_blacklisted_for_skimlinks(
    story_id: Union[int, str], site: Optional[Site] = None
) -> bool:
    """
    Check if the story is blacklisted for Skimlinks
    """
    story_pk: int = int(story_id) if isinstance(story_id, str) else story_id

    if not site:
        site = current_site()

    domain = get_production_domain(site)

    blacklisted_ids: set[int] = SKIMLINKS_STORY_BLACKLIST_BY_DOMAIN.get(
        domain, set()
    )

    return story_pk in blacklisted_ids


def format_collection_page(page, collection):
    item = {
        "name": page.name,
        "url": page.get_absolute_url(),
        "id": page.id,
        "menu_name": page.menu_name,
        "new_window": page.new_window,
        "menu_visible": page.menu_visible,
        "children": [],
        "card_image_url": page.card_image.name if page.card_image else None,
        "meta_description": page.meta_description,
    }

    if collection.include_descendants:
        item["children"] = get_collection_sub_pages(page, collection)
    return item


def get_collection_sub_pages(page, collection):
    sub_pages = []
    child_pages = pull_sub_pages(page)
    if child_pages:
        for child_page in child_pages:
            sub_pages.append(format_collection_page(child_page, collection))
    return sub_pages


def get_story_author_piano_uids(story):
    """
    Get the Piano ID's mapped to staff member email addresses.
    Supports multiple authors with different email addresses.

    There are multiple email addresses a staff member could potentially use with Piano.
    e.g. @austcommunitymedia.com.au for their corporate email and
    @canberratimes.com.au for their masthead and author profile email.
    Staff are currently creating their piano accounts with a mix of these
    emails hence we need to support multiple emails.
    """
    if not story:
        return None
    authors = getattr(story, "authors_detail", None)
    if not authors:
        return None
    emails = set()
    for author in authors:
        if getattr(author, "email", None):
            emails.add(author.email.lower())
        if getattr(author, "user", None) and author.user.get("email"):
            emails.add(author.user["email"].lower())
    return list(
        AuthorPianoUser.objects.filter(email__in=emails).values_list(
            "uid", flat=True
        )
    )
