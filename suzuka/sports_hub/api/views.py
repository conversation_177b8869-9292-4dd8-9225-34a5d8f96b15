from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters, status, viewsets
from rest_framework.decorators import action
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.response import Response

from suzuka.sports_hub.models import (
    Competition,
    Ladder,
    Match,
    MatchFixture,
    MatchStats,
)

from .filters import CompetitionFilter, LadderFilter, MatchFixtureFilter
from .serializers import (
    CompetitionSerializer,
    LadderSerializer,
    MatchFixtureSerializer,
    MatchSerializer,
    MatchStatsSerializer,
)

RESPONSE_CACHE_TIMEOUT = 5


class CustomPagination(LimitOffsetPagination):
    def get_paginated_response(self, data):
        return Response(
            {
                "next_offset": self.offset + self.limit
                if self.offset + self.limit < self.count
                else None,
                "previous_offset": self.offset - self.limit
                if self.offset > 0 and self.offset - self.limit > 0
                else None,
                "count": self.count,
                "results": data,
            }
        )


class BaseViewSet(viewsets.ReadOnlyModelViewSet):
    pagination_class = CustomPagination
    permission_classes = (IsAuthenticatedOrReadOnly,)
    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    )

    @method_decorator(cache_page(RESPONSE_CACHE_TIMEOUT))
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)


class CompetitionViewSet(BaseViewSet):
    filterset_class = CompetitionFilter
    ordering = ["name"]
    ordering_fields = "__all__"
    queryset = Competition.objects.all()
    search_fields = ["name", "sport"]
    serializer_class = CompetitionSerializer


class MatchFixtureViewSet(BaseViewSet):
    filterset_class = MatchFixtureFilter
    ordering = ["match__start_date_time"]
    ordering_fields = [
        "match__round_number",
        "match__match_number",
        "match__start_date_time",
    ]
    queryset = MatchFixture.objects.select_related(
        "match", "match__comp", "match__comp__ladder"
    ).all()
    serializer_class = MatchFixtureSerializer

    @action(
        methods=["get"],
        detail=False,
        url_path="current-round",
        url_name="current_round",
    )
    def get_current_round(self, request, *args, **kwargs):
        match_kwargs = {}
        comp_level_id = None
        if comp_id := request.query_params.get("match__comp__id"):
            match_kwargs["comp_id"] = comp_id
        elif feed_comp_id := request.query_params.get("match__comp__comp_id"):
            match_kwargs["feed_comp_id"] = feed_comp_id
        elif comp_level_id := request.query_params.get(
            "match__comp__comp_level_id"
        ):
            match_kwargs["comp_level_id"] = comp_level_id
        elif comp_level_id__in := request.query_params.get(
            "match__comp__comp_level_id__in"
        ):
            match_kwargs["comp_level_id__in"] = comp_level_id__in
        else:
            return Response(
                {
                    "error": "Either match__comp__id or match__comp__comp_id or match__comp__comp_level_id or match__comp__comp_level_id__in filter is required"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if season := request.query_params.get("match__comp__season"):
            match_kwargs["season"] = season
        else:
            return Response(
                {"error": "match__season filter is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if match_status__in := request.query_params.get(
            "match__match_status__in"
        ):
            match_kwargs["match_status__in"] = match_status__in

        if match_status := request.query_params.get("match__match_status"):
            match_kwargs["match_status"] = match_status

        round_number = Match.objects.current_round(**match_kwargs)

        queryset = self.filter_queryset(self.get_queryset())

        is_finals = False
        if "all_finals" in request.query_params and comp_level_id:
            # NOTE: In order to handle finals case in a single request, we need to detect
            # we are in a finals round and return ALL the final rounds matches.
            try:
                comp_level_id = int(comp_level_id)
            except ValueError:
                return Response(
                    {"error": "comp_level_id must be int."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            finals_round = Competition.get_finals_round(comp_level_id)
            if finals_round and round_number >= finals_round:
                is_finals = True
                queryset = queryset.filter(
                    match__round_number__gte=finals_round
                )

        if not is_finals:
            queryset = queryset.filter(match__round_number=round_number)

        if page := self.paginate_queryset(queryset):
            serializer = self.get_serializer(page, many=True)
            serialized_data = {
                "current_round": round_number,
                "matches": serializer.data,
            }
            return self.get_paginated_response(serialized_data)

        serializer = self.get_serializer(queryset, many=True)
        serialized_data = {
            "current_round": round_number,
            "matches": serializer.data,
        }
        return Response(serialized_data)


class MatchStatsViewSet(BaseViewSet):
    ordering = ["id"]
    ordering_fields = "__all__"
    queryset = MatchStats.objects.select_related(
        "match", "match__comp", "match__comp__ladder"
    ).all()
    serializer_class = MatchStatsSerializer


class MatchViewSet(BaseViewSet):
    ordering = ["start_date_time"]
    ordering_fields = "__all__"
    queryset = Match.objects.select_related(
        "comp", "comp__ladder", "extra", "fixture", "stats"
    ).all()
    serializer_class = MatchSerializer


class LadderViewSet(BaseViewSet):
    filterset_class = LadderFilter
    ordering = ["id"]
    ordering_fields = "__all__"
    queryset = Ladder.objects.select_related("comp").all()
    search_fields = ["comp__name", "comp__season", "comp__sport"]
    serializer_class = LadderSerializer
