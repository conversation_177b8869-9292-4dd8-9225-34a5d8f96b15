# Generated by Django 3.1.14 on 2025-11-17 04:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('sports_hub', '0004_add_cricket'),
    ]

    operations = [
        migrations.AlterField(
            model_name='competition',
            name='comp_level_id',
            field=models.PositiveIntegerField(choices=[(14, 'AFL'), (73, 'A-League'), (3, 'Cricket T20'), (1, 'Cricket Ashes'), (32, 'NRL'), (34, 'State Of Origin')]),
        ),
        migrations.AlterField(
            model_name='match',
            name='match_type',
            field=models.CharField(blank=True, choices=[('P', 'Pre Season'), ('H', 'Home & Away'), ('F', 'Finals'), ('3', 'Cricket T20 game'), ('1', 'Cricket Ashes game')], default='H', max_length=1),
        ),
    ]
