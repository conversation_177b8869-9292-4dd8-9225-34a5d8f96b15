from datetime import datetime
from typing import Any, Optional, cast

from django.db import models

MAX_LEN_COMP_NAME = 50
MAX_LEN_CSS_COLOR = 8
MAX_LEN_MATCH_STATUS = 30
MAX_LEN_MATCH_TYPE = 1
MAX_LEN_SEASON = 20
MAX_LEN_SPORT = 10
MAX_LEN_SQUAD_NAME = 50


class Competition(models.Model):
    SPORT_AFL = "afl"
    SPORT_CRICKET = "cricket"
    SPORT_LEAGUE = "league"
    SPORT_SOCCER = "soccer"

    SPORT_CHOICES = [
        (SPORT_AFL, "AFL"),
        (SPORT_CRICKET, "Cricket"),
        (SPORT_LEAGUE, "League"),
        (SPORT_SOCCER, "Soccer"),
    ]

    COMP_LEVEL_ID_AFL = 14
    COMP_LEVEL_ID_ALEAGUE = 73
    COMP_LEVEL_ID_CRICKET_T20 = 3  # any T20 comp (= matchType)
    COMP_LEVEL_ID_CRICKET_ASHES = 1
    COMP_LEVEL_ID_NRL = 32
    COMP_LEVEL_ID_STATE_OF_ORIGIN = 34

    FINALS_ROUND = {
        COMP_LEVEL_ID_AFL: 25,
        COMP_LEVEL_ID_NRL: 28,
        COMP_LEVEL_ID_ALEAGUE: 27,
        COMP_LEVEL_ID_STATE_OF_ORIGIN: 28,
    }

    COMP_LEVEL_ID_CHOICES = [
        (COMP_LEVEL_ID_AFL, "AFL"),
        (COMP_LEVEL_ID_ALEAGUE, "A-League"),
        (COMP_LEVEL_ID_CRICKET_T20, "Cricket T20"),
        (COMP_LEVEL_ID_CRICKET_ASHES, "Cricket Ashes"),
        (COMP_LEVEL_ID_NRL, "NRL"),
        (COMP_LEVEL_ID_STATE_OF_ORIGIN, "State Of Origin"),
    ]

    sport = models.CharField(max_length=MAX_LEN_SPORT, choices=SPORT_CHOICES)
    name = models.CharField(max_length=MAX_LEN_COMP_NAME)
    comp_id = models.PositiveIntegerField(unique=True)
    comp_level_id = models.PositiveIntegerField(choices=COMP_LEVEL_ID_CHOICES)
    season = models.CharField(max_length=MAX_LEN_SEASON)

    def __str__(self):
        if self.sport and self.name:
            sport = dict(self.SPORT_CHOICES).get(self.sport, self.sport)
            return f"{sport} - {self.name} for {self.season}"
        return super().__str__()

    @classmethod
    def get_finals_round(cls, comp_level_id: int) -> Optional[int]:
        return cls.FINALS_ROUND.get(comp_level_id)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["comp_level_id", "season"],
                name="unique_comp_level_season",
            )
        ]
        ordering = ["name"]


class MatchManager(models.Manager):
    def current_round(
        self,
        comp_id: Optional[int] = None,
        comp_level_id: Optional[int] = None,
        comp_level_id__in: Optional[str] = None,
        feed_comp_id: Optional[int] = None,
        match_status: Optional[str] = None,
        match_status__in: Optional[str] = None,
        season: Optional[str] = None,
    ) -> int:
        utc_now = datetime.utcnow()
        utc_now_date = utc_now.date()

        match_filter: dict[str, Any] = {}

        if comp_id:
            match_filter["comp__id"] = comp_id
        if feed_comp_id:
            match_filter["comp__comp_id"] = feed_comp_id
        if comp_level_id:
            match_filter["comp__comp_level_id"] = comp_level_id
        if comp_level_id__in:
            match_filter["comp__comp_level_id__in"] = comp_level_id__in.split(
                ",",
            )
        if season:
            match_filter["comp__season"] = season
        if match_status:
            match_filter["match_status"] = match_status
        if match_status__in:
            match_filter["match_status__in"] = match_status__in.split(",")

        match = cast(
            Optional["Match"],
            (
                super()
                .get_queryset()
                .filter(
                    **match_filter, start_date_time__date__gte=utc_now_date
                )
                .order_by("start_date_time")
                .first()
            ),
        )
        if match:
            return match.round_number

        last_round: dict = (
            super()
            .get_queryset()
            .filter(**match_filter)
            .aggregate(max_round_number=models.Max("round_number"))
        )
        if max_round_number := last_round["max_round_number"]:
            return max_round_number

        return 1


class Match(models.Model):
    MATCH_STATUS_COMPLETE = "complete"
    MATCH_STATUS_COMPLETED = "completed"  # Cricket only
    MATCH_STATUS_DRINKS = "drinks"  # Cricket only
    MATCH_STATUS_END_OF_DAY = "endOfDay"  # Cricket only
    MATCH_STATUS_END_OF_INNINGS = "endOfInnings"  # Cricket only
    MATCH_STATUS_LUNCH = "lunch"  # Cricket only
    MATCH_STATUS_PLAYING = "playing"
    MATCH_STATUS_POSTMATCH = "postmatch"
    MATCH_STATUS_PREMATCH = "prematch"
    MATCH_STATUS_RAIN_INTERRUPTION = "rainInterruption"  # Cricket only
    MATCH_STATUS_SCHEDULED = "scheduled"
    MATCH_STATUS_START_DELAYED = "startDelayed"  # Cricket only
    MATCH_STATUS_TEA = "tea"  # Cricket only

    MATCH_TYPE_FINALS = "F"
    MATCH_TYPE_HOME_AWAY = "H"
    MATCH_TYPE_PRE_SEASON = "P"
    MATCH_TYPE_TEST_MATCH_ASHES = "1"  # Cricket only (Ashes)
    MATCH_TYPE_TEST_MATCH = "3"  # Cricket only (means just any T20 comp)

    MATCH_STATUS_CHOICES = [
        (MATCH_STATUS_SCHEDULED, "Scheduled"),
        (MATCH_STATUS_PREMATCH, "Pre Match"),
        (MATCH_STATUS_START_DELAYED, "Start Delayed (Cricket)"),
        (MATCH_STATUS_PLAYING, "Playing"),
        (MATCH_STATUS_DRINKS, "Drinks (Cricket)"),
        (MATCH_STATUS_LUNCH, "Lunch (Cricket)"),
        (MATCH_STATUS_TEA, "Tea (Cricket)"),
        (MATCH_STATUS_RAIN_INTERRUPTION, "Rain Interruption (Cricket)"),
        (MATCH_STATUS_END_OF_DAY, "End of Day (Cricket)"),
        (MATCH_STATUS_END_OF_INNINGS, "End of Innings (Cricket)"),
        (MATCH_STATUS_COMPLETE, "Complete"),
        (MATCH_STATUS_COMPLETED, "Completed (Cricket)"),
        (MATCH_STATUS_POSTMATCH, "Post Match"),
    ]

    MATCH_TYPE_CHOICES = [
        (MATCH_TYPE_PRE_SEASON, "Pre Season"),
        (MATCH_TYPE_HOME_AWAY, "Home & Away"),
        (MATCH_TYPE_FINALS, "Finals"),
        (MATCH_TYPE_TEST_MATCH, "Cricket T20 game"),
        (MATCH_TYPE_TEST_MATCH_ASHES, "Cricket Ashes game"),
    ]

    comp = models.ForeignKey(
        "sports_hub.Competition",
        on_delete=models.deletion.CASCADE,
        related_name="%(class)ss",
    )
    round_number = models.PositiveSmallIntegerField(blank=True, default=1)
    match_number = models.PositiveSmallIntegerField(blank=True, default=1)
    match_type = models.CharField(
        blank=True,
        choices=MATCH_TYPE_CHOICES,
        default=MATCH_TYPE_HOME_AWAY,
        max_length=MAX_LEN_MATCH_TYPE,
    )
    match_status = models.CharField(
        blank=True,
        choices=MATCH_STATUS_CHOICES,
        default=MATCH_STATUS_SCHEDULED,
        max_length=MAX_LEN_MATCH_STATUS,
    )
    start_date_time = models.DateTimeField()
    match_id = models.PositiveIntegerField(unique=True)

    objects = MatchManager()

    def __str__(self):
        if self.comp_id:
            match_status = dict(self.MATCH_STATUS_CHOICES).get(
                self.match_status, self.match_status
            )
            return f"{self.comp} - {match_status} match {self.match_id}"
        return super().__str__()

    class Meta:
        verbose_name_plural = "matches"


class MatchExtra(models.Model):
    match = models.OneToOneField(
        "sports_hub.Match",
        on_delete=models.deletion.CASCADE,
        related_name="extra",
    )
    away_squad_preview = models.TextField("Away team", blank=True, default="")
    home_squad_preview = models.TextField("Home team", blank=True, default="")

    class Meta:
        verbose_name = "Insights"
        verbose_name_plural = "Insights"

    def __str__(self):
        if self.match_id:
            return f"{self.match}"
        return super().__str__()


class MatchFixture(models.Model):
    match = models.OneToOneField(
        "sports_hub.Match",
        on_delete=models.deletion.CASCADE,
        related_name="fixture",
    )
    data = models.JSONField(blank=True, default=dict)

    def __str__(self):
        if self.match_id:
            return f"{self.match}"
        return super().__str__()


class MatchStats(models.Model):
    match = models.OneToOneField(
        "sports_hub.Match",
        on_delete=models.deletion.CASCADE,
        related_name="stats",
    )
    data = models.JSONField(blank=True, default=dict)

    class Meta:
        verbose_name_plural = "match stats"

    def __str__(self):
        if self.match_id:
            return f"{self.match}"
        return super().__str__()


class Ladder(models.Model):
    comp = models.OneToOneField(
        "sports_hub.Competition",
        related_name="%(class)s",
        on_delete=models.deletion.CASCADE,
    )
    data = models.JSONField(blank=True, default=dict)

    def __str__(self):
        if self.comp_id:
            return f"{self.comp}"
        return super().__str__()
