from django.core.management.base import BaseCommand
from django.db.models import Q

from suzuka.conf.models import Settings
from suzuka.stories.utils import author_api
from suzuka.subscriptions.models import AuthorPianoUser
from suzuka.subscriptions.piano import PianoClient, PianoException


class Command(BaseCommand):
    help = "Sync silverstone authors with PianoAuthorUsers"

    sites = (
        Settings.objects.select_related("features")
        .filter(
            visible=True,
            features__pianofeature_enabled=True,
            domain__startswith="www",
        )
        .exclude(
            Q(pianofeature_piano_aid="") | Q(pianofeature_piano_api_token="")
        )
    )

    def get_authors(self):
        def get_authors_from_api(limit=1000, offset=0, authors=None):
            if authors is None:
                authors = []
            response = author_api().author().get(limit=limit, offset=offset)
            authors.extend(response.get("objects", []))
            if response.get("meta", {}).get("next"):
                return get_authors_from_api(limit, offset + limit, authors)
            return authors

        return get_authors_from_api()

    def get_piano_user(self, piano_client, site, email):
        piano_client.set_auth(
            site.pianofeature_piano_aid, site.pianofeature_piano_api_token
        )
        response = piano_client.search_users(email=email)
        user = None
        try:
            for x in response:
                # Check to ensure the email matches since this is a search api
                if x.get("email") == email:
                    user = x
                    break
        except PianoException as e:
            self.stdout.write(
                self.style.WARNING(f"Piano API error for {email}: {e}")
            )
        if user:
            self.stdout.write(
                self.style.SUCCESS(
                    f"User {user.get('uid')} found for {email}. aid: {site.pianofeature_piano_aid} ({site.domain})"
                )
            )
        else:
            self.stdout.write(
                self.style.WARNING(
                    f"User not found for {email}. aid: {site.pianofeature_piano_aid} ({site.domain})"
                )
            )
        return user

    def order_sites_by_default_org(self, default_org_id=None):
        sites = list(self.sites)
        if default_org_id:
            sites.sort(
                key=lambda x: x.organization == default_org_id, reverse=True
            )
        return sites

    def handle(self, *args, **options):
        authors = self.get_authors()
        piano_client = PianoClient()
        for author in authors:
            emails = set()
            if getattr(author, "email", None):
                emails.add(author.email)
            if getattr(author, "user", None) and getattr(
                author.user, "email", None
            ):
                emails.add(author.user.email)
            org_id = getattr(author, "default_organization", None)
            if not emails:
                self.stdout.write(
                    self.style.ERROR(f"Author {author.id} has no email")
                )
                continue
            sites = self.order_sites_by_default_org(org_id)
            for site in sites:
                found = False
                for email in emails:
                    user = self.get_piano_user(piano_client, site, email)
                    if user:
                        AuthorPianoUser.objects.update_or_create(
                            email=email.lower(),
                            defaults={
                                "uid": user.get("uid"),
                                "aid": site.pianofeature_piano_aid,
                            },
                        )
                        found = True
                if found:
                    break

        self.stdout.write(
            self.style.SUCCESS(
                "Silverstone authors synced with Piano successfully"
            )
        )
