from unittest.mock import Mock, patch

from django.conf import settings
from django.db.utils import IntegrityError
from django.test import TestCase

from suzuka.conf.models import Settings
from suzuka.conf.tests import MultiSiteTest

from .models import Cluster, ClusterMember
from .utils import cluster_summary_for_site, encrypt_piano_data


def configure_retently_mocks(mock_get, mock_post):
    mock_get.return_value = {
        "uid": "PNIOTRaxhq7d1ov",
        "first_name": "Test",
        "last_name": "Example",
        "email": "<EMAIL>",
        "create_date": "2021-01-01 12:00:00",
    }

    post_response = Mock()
    post_response.status_code = 200
    mock_post.side_effect = [post_response]


@patch(
    "suzuka.mailinglists.mailchimp.Mailchimp.create_or_update_subscriber",
    Mock(),
)
@patch(
    "suzuka.subscriptions.piano.PianoClient.get_user_subscription",
    lambda *_: None,
)
class PianoWebhookTest(MultiSiteTest):
    def setUp(self):
        """
        Enable Piano & Retently for Piano Webhook testing
        """
        super().setUp()

        # Enable piano to enable PianoWebhookView
        self.site1.features.pianofeature_enabled = True
        self.site1.pianofeature_piano_aid = "KO7eJSSkIt"
        self.site1.pianofeature_piano_private_key = (
            "JyWmjQV77YUhSqUPsN5JVT2bmd2V52o8w9DN3FyE"
        )
        self.site1.pianofeature_piano_api_token = (
            "hl433DhGfaNKEfEWOjqIndSWnz75XvQcH7P7W2IY"
        )

        # Enable retently feature for cancelled test
        self.site1.features.retentlyfeature_enabled = True
        self.site1.retentlyfeature_retently_track_revoked_access = True

        self.site1.features.save()
        self.site1.save()

    @patch("suzuka.subscriptions.views.get_sns_topic")
    @patch("suzuka.subscriptions.views.get_cluster_id_for_site")
    @patch(
        "suzuka.subscriptions.views.settings.PIANO_WEBHOOK_SNS_TOPIC",
        "arn:aws:sns:us-east-1:123456789012:piano-webhook-topic",
    )
    def test_access_revoked_event_cancelled(
        self, mock_get_cluster_id, mock_get_sns_topic
    ):
        """
        Ensure that a webhook of type `access revoked` with event
        `subscription_cancelled` leads to an SNS message being sent
        """
        mock_sns_client = Mock()
        mock_get_sns_topic.return_value = mock_sns_client
        mock_get_cluster_id.return_value = "test-cluster-id"

        piano_webhook_data = {
            "type": "access_revoked",
            "event": "subscription_canceled",
            "uid": "PNIOTRaxhq7d1ov",
        }

        piano_webhook_data_encrypted = encrypt_piano_data(
            self.site1.settings.pianofeature_piano_private_key,
            piano_webhook_data,
        )

        response = self.get_for_site(
            self.site1,
            "/subscriptions/piano-webhook/",
            {"data": piano_webhook_data_encrypted},
        )

        self.assertTrue(
            mock_sns_client.publish.called,
            "Webhook data did not lead to SNS message being sent",
        )
        self.assertEqual(
            response.status_code,
            200,
            "Webhook did not return a 200",
        )

        # Verify get_cluster_id_for_site was called with use_cache=False
        mock_get_cluster_id.assert_called_once()
        # Verify it was called with the correct arguments
        call_args_cluster = mock_get_cluster_id.call_args
        self.assertEqual(call_args_cluster[1]["use_cache"], False)
        # Verify it was called with the correct site by checking the domain
        called_site = call_args_cluster[0][0]
        self.assertEqual(called_site.domain, self.site1.domain)

        # Verify the SNS message contains the expected data
        call_args = mock_sns_client.publish.call_args
        self.assertEqual(
            call_args[1]["TopicArn"],
            "arn:aws:sns:us-east-1:123456789012:piano-webhook-topic",
        )
        self.assertEqual(
            call_args[1]["MessageGroupId"],
            "test-cluster-id",
        )
        message_body = call_args[1]["Message"]
        import json

        webhook_data = json.loads(message_body)
        self.assertEqual(
            webhook_data["site_setting_id"], self.site1.settings.id
        )
        self.assertEqual(webhook_data["site_id"], self.site1.id)
        self.assertEqual(webhook_data["site_domain"], self.site1.domain)
        self.assertEqual(webhook_data["user_id"], "PNIOTRaxhq7d1ov")
        self.assertEqual(webhook_data["event_type"], "access_revoked")
        self.assertEqual(webhook_data["event"], "subscription_canceled")

    @patch("suzuka.subscriptions.views.get_sns_topic")
    @patch("suzuka.subscriptions.views.get_cluster_id_for_site")
    @patch(
        "suzuka.subscriptions.views.settings.PIANO_WEBHOOK_SNS_TOPIC",
        "arn:aws:sns:us-east-1:123456789012:piano-webhook-topic",
    )
    def test_access_revoked_event_expired(
        self, mock_get_cluster_id, mock_get_sns_topic
    ):
        """
        Ensure that a webhook of type `access revoked` with event
        `subscription_expired` sends an SNS message
        """
        mock_sns_client = Mock()
        mock_get_sns_topic.return_value = mock_sns_client
        mock_get_cluster_id.return_value = "test-cluster-id"

        piano_webhook_data = {
            "type": "access_revoked",
            "event": "subscription_expired",
            "uid": "PNIOTRaxhq7d1ov",
        }

        piano_webhook_data_encrypted = encrypt_piano_data(
            self.site1.settings.pianofeature_piano_private_key,
            piano_webhook_data,
        )

        response = self.get_for_site(
            self.site1,
            "/subscriptions/piano-webhook/",
            {"data": piano_webhook_data_encrypted},
        )

        self.assertTrue(
            mock_sns_client.publish.called,
            "Webhook data did not lead to SNS message being sent",
        )
        self.assertEqual(
            response.status_code,
            200,
            "Webhook did not return a 200",
        )

        # Verify get_cluster_id_for_site was called with use_cache=False
        mock_get_cluster_id.assert_called_once()
        call_args_cluster = mock_get_cluster_id.call_args
        self.assertEqual(call_args_cluster[1]["use_cache"], False)
        called_site = call_args_cluster[0][0]
        self.assertEqual(called_site.domain, self.site1.domain)

        # Verify the SNS message includes MessageGroupId
        call_args = mock_sns_client.publish.call_args
        self.assertEqual(
            call_args[1]["MessageGroupId"],
            "test-cluster-id",
        )

    @patch("suzuka.subscriptions.views.get_sns_topic")
    @patch("suzuka.subscriptions.views.get_cluster_id_for_site")
    @patch(
        "suzuka.subscriptions.views.settings.PIANO_WEBHOOK_SNS_TOPIC",
        "arn:aws:sns:us-east-1:123456789012:piano-webhook-topic",
    )
    def test_sns_publish_error(self, mock_get_cluster_id, mock_get_sns_topic):
        """Test that SNS publish errors are handled gracefully."""
        mock_sns_client = Mock()
        mock_sns_client.publish.side_effect = Exception("SNS error")
        mock_get_sns_topic.return_value = mock_sns_client
        mock_get_cluster_id.return_value = "test-cluster-id"

        piano_webhook_data = {
            "type": "access_revoked",
            "event": "subscription_expired",
            "uid": "PNIOTRaxhq7d1ov",
        }

        piano_webhook_data_encrypted = encrypt_piano_data(
            self.site1.settings.pianofeature_piano_private_key,
            piano_webhook_data,
        )

        response = self.get_for_site(
            self.site1,
            "/subscriptions/piano-webhook/",
            {"data": piano_webhook_data_encrypted},
        )

        self.assertEqual(response.status_code, 500)

        # Verify get_cluster_id_for_site was called even when SNS fails
        mock_get_cluster_id.assert_called_once()
        call_args_cluster = mock_get_cluster_id.call_args
        self.assertEqual(call_args_cluster[1]["use_cache"], False)
        called_site = call_args_cluster[0][0]
        self.assertEqual(called_site.domain, self.site1.domain)

    @patch("suzuka.subscriptions.views.ViafouraClient")
    @patch("suzuka.subscriptions.views.requests.post")
    @patch("suzuka.subscriptions.piano.PianoClient.get_user")
    def test_handle_webhook_sns_retently(
        self, mock_get_user, mock_post, mock_viafoura_client
    ):
        """Test that the SNS handler calls retently for subscription_canceled events."""
        configure_retently_mocks(mock_get_user, mock_post)
        mock_viafoura_instance = Mock()
        mock_viafoura_client.return_value = mock_viafoura_instance

        webhook_data = {
            "site_setting_id": self.site1.settings.id,
            "site_id": self.site1.id,
            "site_domain": self.site1.domain,
            "user_id": "PNIOTRaxhq7d1ov",
            "event_type": "access_revoked",
            "event": "subscription_canceled",
            "json_data": {
                "type": "access_revoked",
                "event": "subscription_canceled",
                "uid": "PNIOTRaxhq7d1ov",
            },
            "received_at": "2024-01-01T12:00:00",
        }

        from suzuka.subscriptions.views import handle_webhook_sqs

        result = handle_webhook_sqs(webhook_data)

        self.assertTrue(result["success"])
        self.assertTrue(mock_post.called)

    @patch("suzuka.subscriptions.views.ViafouraClient")
    @patch("suzuka.subscriptions.views.requests.post")
    @patch("suzuka.subscriptions.piano.PianoClient.get_user")
    def test_handle_webhook_sns_no_retently_for_expired(
        self, mock_get_user, mock_post, mock_viafoura_client
    ):
        """Test that the SNS handler does not call retently for subscription_expired events."""
        configure_retently_mocks(mock_get_user, mock_post)
        mock_viafoura_instance = Mock()
        mock_viafoura_client.return_value = mock_viafoura_instance

        webhook_data = {
            "site_setting_id": self.site1.settings.id,
            "site_id": self.site1.id,
            "site_domain": self.site1.domain,
            "user_id": "PNIOTRaxhq7d1ov",
            "event_type": "access_revoked",
            "event": "subscription_expired",
            "json_data": {
                "type": "access_revoked",
                "event": "subscription_expired",
                "uid": "PNIOTRaxhq7d1ov",
            },
            "received_at": "2024-01-01T12:00:00",
        }

        from suzuka.subscriptions.views import handle_webhook_sqs

        result = handle_webhook_sqs(webhook_data)

        self.assertTrue(result["success"])
        self.assertFalse(mock_post.called)


def create_sites(nbr, prefix="www"):
    name_suffix = " Beta" if prefix == "beta" else ""
    return [
        Settings.objects.create(
            visible=True,
            name=f"Test {site_nbr}{name_suffix}",
            domain=f"{prefix}-test{site_nbr}-com.test.it",
            organization=f"{site_nbr + 1}",
            static_dir=f"test{site_nbr}",
            theme_dir="autumn",
        ).site_ptr
        for site_nbr in range(nbr)
    ]


class ClusterTestCase(TestCase):
    def setUp(self):
        self._www_sites = create_sites(4)
        self._beta_sites = create_sites(4, prefix="beta")

    def test_summary_for_no_clusters(self):
        summary = cluster_summary_for_site(self._www_sites[0])
        self.assertEqual(summary, None)

    def test_summary_for_solo_self_cluster(self):
        cluster = Cluster.objects.create(
            name="cluster1", content="cluster1 content"
        )
        ClusterMember.objects.create(
            cluster=cluster,
            organization=self._www_sites[0].settings.organization,
        )
        summary = cluster_summary_for_site(self._www_sites[0])
        self.assertEqual(
            summary,
            {
                "sites": [],
                "content": "cluster1 content",
                "cluster_name": "cluster1",
                "cluster_id": cluster.id,
            },
        )

    def test_summary_for_cluster_members(self):
        cluster1 = Cluster.objects.create(
            name="cluster1", content="cluster1 content", slug="cluster1"
        )
        ClusterMember.objects.create(
            cluster=cluster1,
            content="cluster 1 member 0 content",
            is_primary=True,
            organization=self._www_sites[0].settings.organization,
            promoted_organization=self._www_sites[3].settings.organization,
        )
        ClusterMember.objects.create(
            cluster=cluster1,
            organization=self._www_sites[1].settings.organization,
            promoted_organization=self._www_sites[2].settings.organization,
        )
        ClusterMember.objects.create(
            cluster=cluster1,
            organization=self._www_sites[2].settings.organization,
            promoted_organization=self._www_sites[3].settings.organization,
        )
        ClusterMember.objects.create(
            cluster=cluster1,
            organization=self._www_sites[3].settings.organization,
        )
        cluster2 = Cluster.objects.create(
            name="cluster2",
            content="cluster2 content",
            slug="cluster2",
        )
        ClusterMember.objects.create(
            cluster=cluster2,
            content="cluster 2 member 0 content",
            organization=self._www_sites[0].settings.organization,
        )
        summary = cluster_summary_for_site(self._www_sites[0])
        static_base = f"{settings.STATIC_URL}sites"
        self.assertEqual(
            summary,
            {
                "sites": [
                    {
                        "domain": "www-test3-com.test.it",
                        "location": None,
                        "logo_svg_only": f"{static_base}/test3/images/masthead/masthead-only.svg",
                        "logo_svg_square": f"{static_base}/test3/images/masthead/masthead-square.svg",
                        "name": "Test 3",
                        "org_id": self._www_sites[3].settings.organization,
                        "promoted": True,
                    },
                    {
                        "domain": "www-test1-com.test.it",
                        "location": None,
                        "logo_svg_square": f"{static_base}/test1/images/masthead/masthead-square.svg",
                        "name": "Test 1",
                        "org_id": self._www_sites[1].settings.organization,
                    },
                    {
                        "domain": "www-test2-com.test.it",
                        "location": None,
                        "logo_svg_square": f"{static_base}/test2/images/masthead/masthead-square.svg",
                        "name": "Test 2",
                        "org_id": self._www_sites[2].settings.organization,
                    },
                ],
                "content": "cluster 1 member 0 content",
                "cluster_name": "cluster1",
                "cluster_id": cluster1.id,
            },
        )

    def test_summary_for_cluster_members_beta(self):
        cluster1 = Cluster.objects.create(
            name="cluster1",
            content="cluster1 content",
            slug="cluster1",
        )
        ClusterMember.objects.create(
            cluster=cluster1,
            content="cluster 1 member 0 content",
            is_primary=True,
            organization=self._beta_sites[0].settings.organization,
            promoted_organization=self._beta_sites[3].settings.organization,
        )
        ClusterMember.objects.create(
            cluster=cluster1,
            organization=self._beta_sites[1].settings.organization,
            promoted_organization=self._beta_sites[2].settings.organization,
        )
        ClusterMember.objects.create(
            cluster=cluster1,
            organization=self._beta_sites[2].settings.organization,
            promoted_organization=self._beta_sites[3].settings.organization,
        )
        ClusterMember.objects.create(
            cluster=cluster1,
            organization=self._beta_sites[3].settings.organization,
        )
        cluster2 = Cluster.objects.create(
            name="cluster2",
            content="cluster2 content",
            slug="cluster2",
        )
        ClusterMember.objects.create(
            cluster=cluster2,
            content="cluster 2 member 0 content",
            organization=self._beta_sites[0].settings.organization,
        )
        summary = cluster_summary_for_site(self._beta_sites[0])
        static_base = f"{settings.STATIC_URL}sites"
        self.assertEqual(
            summary,
            {
                "sites": [
                    {
                        "domain": "beta-test3-com.test.it",
                        "location": None,
                        "logo_svg_only": f"{static_base}/test3/images/masthead/masthead-only.svg",
                        "logo_svg_square": f"{static_base}/test3/images/masthead/masthead-square.svg",
                        "name": "Test 3 Beta",
                        "org_id": self._www_sites[3].settings.organization,
                        "promoted": True,
                    },
                    {
                        "domain": "beta-test1-com.test.it",
                        "location": None,
                        "logo_svg_square": f"{static_base}/test1/images/masthead/masthead-square.svg",
                        "name": "Test 1 Beta",
                        "org_id": self._www_sites[1].settings.organization,
                    },
                    {
                        "domain": "beta-test2-com.test.it",
                        "location": None,
                        "logo_svg_square": f"{static_base}/test2/images/masthead/masthead-square.svg",
                        "name": "Test 2 Beta",
                        "org_id": self._www_sites[2].settings.organization,
                    },
                ],
                "content": "cluster 1 member 0 content",
                "cluster_name": "cluster1",
                "cluster_id": cluster1.id,
            },
        )

    def test_same_cluster_member_constraints(self):
        cluster1 = Cluster.objects.create(name="cluster1")
        ClusterMember.objects.create(
            cluster=cluster1,
            is_primary=True,
            organization=self._www_sites[0].settings.organization,
        )
        with self.assertRaises(IntegrityError):
            ClusterMember.objects.create(
                cluster=cluster1,
                is_primary=True,
                organization=self._www_sites[0].settings.organization,
            )

    def test_different_cluster_member_constraints(self):
        cluster1 = Cluster.objects.create(name="cluster1", slug="cluster1")
        cluster2 = Cluster.objects.create(name="cluster2", slug="cluster2")
        ClusterMember.objects.create(
            cluster=cluster1,
            is_primary=True,
            organization=self._www_sites[0].settings.organization,
        )
        with self.assertRaises(IntegrityError):
            ClusterMember.objects.create(
                cluster=cluster2,
                is_primary=True,
                organization=self._www_sites[0].settings.organization,
            )
