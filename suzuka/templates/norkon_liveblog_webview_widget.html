{# https://support.norkon.net/live-center-integrations#javascript-implementation #}
{% if norkon_liveblog_enabled %}
    <link href="{{ base_css_url }}" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,700,800&display=swap" rel="stylesheet" />
    <link href="{{ extension_css_url }}" rel="stylesheet" />
    <div class="lc-frame lc-default-theme">
        <div class="lc-feed-container">
            <div id="master-container"></div>
            <div style="text-align: center">
                <button class="lc-load-more" id="lc-load-more" type="button">
                    Load more
                </button>
            </div>
        </div>
    </div>
    <script src="{{ base_js_url }}"></script>
    <script src="{{ extension_js_url }}"></script>
    <script type="text/javascript">
        window.APP_ENV = "{{ app_env|escapejs }}";
        window.NcPosts.start({
            channelId: "{{ service_id|escapejs }}",
            tenantKey: "{{ tenant_key|escapejs }}",
            container: document.getElementById('master-container'),
            extensionContainer: window.NcLiveCenterExtensions,
            showMoreElement: document.getElementById('lc-load-more'),
            baseUrl: "{{ base_url|escapejs }}",
            wsBaseUrl: "{{ websocket_url|escapejs }}"
        });
    </script>
{% endif %}